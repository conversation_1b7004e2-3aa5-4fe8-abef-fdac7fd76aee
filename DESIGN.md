# MCP Server Proxy 设计方案

## 1. 项目概述

### 1.1 背景
Model Context Protocol (MCP) 是一个开放协议，用于LLM应用与外部数据源和工具的无缝集成。本项目实现了一个MCP Server Proxy，作为中间层聚合多个MCP服务器，使LLM客户端能够通过单一连接访问多个服务器的功能。

### 1.2 核心目标
- **🔗 多服务器聚合**: 将多个独立的MCP服务器聚合为一个统一的接口
- **🚀 多传输协议支持**: 支持 stdio、HTTP (SSE)、Streamable HTTP 三种传输协议
- **✅ 协议完全兼容**: 完全兼容MCP协议规范，支持所有消息类型
- **🏷️ 命名空间管理**: 通过命名空间避免不同服务器间的命名冲突
- **📡 实时通知转发**: 支持来自后端服务器的实时通知转发
- **🔧 灵活配置**: 配置驱动的传输协议选择和服务器管理
- **🛡️ 健壮性**: 完善的错误处理和连接管理
- **📈 可扩展性**: 模块化设计，易于扩展和维护

## 2. MCP协议理解

### 2.1 协议架构
MCP采用客户端-服务器架构，基于JSON-RPC 2.0协议：

```
LLM Host (如Claude Desktop) 
    ↓ (创建MCP Client)
MCP Client 
    ↓ (JSON-RPC 2.0)
MCP Server (提供context和capabilities)
```

### 2.2 核心概念

#### 2.2.1 生命周期管理
- **初始化握手**: `initialize` → `initialized`
- **能力协商**: 客户端和服务器声明支持的功能
- **连接管理**: 维护有状态连接

#### 2.2.2 三大核心原语
1. **Tools**: 可执行函数 (如文件操作、API调用、数据库查询)
2. **Resources**: 上下文数据源 (如文件内容、数据库记录、API响应)  
3. **Prompts**: 可重用模板 (如系统提示、few-shot示例)

#### 2.2.3 通信流程
1. **发现**: 使用 `*/list` 方法发现可用功能
2. **获取/执行**: 使用 `*/get` 或 `*/call` 方法获取内容或执行操作
3. **通知**: 服务器主动通知客户端状态变化

### 2.3 传输层支持

#### 2.3.1 代理对外传输协议
- **stdio**: 标准输入输出流，最低延迟，适用于命令行工具
- **HTTP (SSE)**: HTTP POST + Server-Sent Events，Web 友好，支持 CORS
- **Streamable HTTP**: 现代双向流式 HTTP，支持会话管理和无状态模式

#### 2.3.2 后端连接传输协议
- **stdio**: 连接本地 MCP 服务器进程
- **HTTP (SSE)**: 连接远程 HTTP MCP 服务器
- **Streamable HTTP**: 连接现代 Streamable HTTP MCP 服务器

#### 2.3.3 混合传输架构
```
代理服务器 (可选择对外传输协议)
    ├── stdio 对外服务
    ├── HTTP (SSE) 对外服务
    └── Streamable HTTP 对外服务
         │
         └── 同时连接多种后端传输协议
             ├── stdio 后端 (Everything Server)
             ├── HTTP 后端 (Simple SSE Server)
             └── Streamable HTTP 后端 (Streamable Server)
```

## 3. Proxy架构设计

### 3.1 整体架构

```mermaid
graph TB
    subgraph "LLM客户端层"
        Client[LLM客户端<br/>Cursor/Claude Desktop]
    end
    
    subgraph "MCP Server Proxy"
        ProxyServer[Proxy Server<br/>JSON-RPC 2.0 Handler]
        
        subgraph "核心模块"
            ConnMgr[连接管理器<br/>Connection Manager]
            Aggregator[能力聚合器<br/>Capability Aggregator]
            Router[请求路由器<br/>Request Router]
            NotifyMgr[通知管理器<br/>Notification Manager]
        end
        
        subgraph "后端连接池"
            BackendMgr[后端管理器<br/>Backend Manager]
            Client1[MCP Client 1]
            Client2[MCP Client 2]
            ClientN[MCP Client N]
        end
    end
    
    subgraph "MCP服务器层"
        Server1[MCP Server 1<br/>文件系统服务]
        Server2[MCP Server 2<br/>数据库服务]
        ServerN[MCP Server N<br/>API服务]
    end
```

### 3.2 模块职责

#### 3.2.1 Proxy Server (代理服务器核心)
- **职责**: MCP协议的主要入口点，处理来自LLM客户端的JSON-RPC 2.0请求
- **功能**: 
  - 协议解析和验证
  - 生命周期管理 (initialize, initialized)
  - 请求分发到相应模块

#### 3.2.2 Backend Manager (后端管理器)
- **职责**: 管理与多个MCP服务器的连接
- **功能**:
  - 维护MCP客户端连接池
  - 处理服务器启动/停止
  - 连接健康检查
  - 加载服务器能力

#### 3.2.3 Capability Aggregator (能力聚合器)
- **职责**: 聚合所有后端MCP服务器的能力
- **功能**:
  - 合并多个服务器的tools/resources/prompts列表
  - 处理能力冲突和命名空间
  - 维护路由映射表

#### 3.2.4 Request Router (请求路由器)
- **职责**: 将客户端请求路由到正确的后端服务器
- **功能**:
  - 根据tool/resource/prompt名称路由请求
  - 支持命名空间前缀 (如 `server1.tool_name`)
  - 错误处理和回退机制

#### 3.2.5 Notification Manager (通知管理器)
- **职责**: 处理来自后端服务器的通知并转发给客户端
- **功能**:
  - 监听后端服务器通知
  - 聚合和去重通知
  - 转发给前端客户端

## 4. 关键技术实现

### 4.1 命名空间机制
为避免不同服务器间的命名冲突，实现命名空间机制：

```typescript
// 配置示例
{
  "id": "filesystem",
  "namespace": "fs",
  // ...
}

// 结果: list_files → fs.list_files
```

### 4.2 请求路由算法
1. 解析请求中的tool/resource/prompt名称
2. 查找路由映射表获取目标服务器
3. 将命名空间名称转换为原始名称
4. 转发请求到目标服务器
5. 处理响应并返回给客户端

### 4.3 通知聚合策略
- **防抖机制**: 避免短时间内重复通知
- **类型聚合**: 将多个服务器的同类型通知合并
- **优先级处理**: 重要通知优先转发

### 4.4 错误处理
- **连接失败**: 记录错误，继续其他服务器
- **请求失败**: 返回标准JSON-RPC错误响应
- **服务器断开**: 自动重连机制

## 5. 配置管理

### 5.1 配置文件格式
```json
{
  "serverInfo": {
    "name": "mcp-server-proxy",
    "version": "1.0.0"
  },
  "servers": [
    {
      "id": "filesystem",
      "name": "Filesystem Server",
      "command": "npx",
      "args": ["@modelcontextprotocol/server-filesystem", "/tmp"],
      "transport": "stdio",
      "namespace": "fs",
      "enabled": true,
      "env": {
        "NODE_ENV": "production"
      }
    }
  ]
}
```

### 5.2 配置加载优先级
1. 命令行参数
2. 环境变量
3. 配置文件
4. 默认配置

## 6. 测试策略

### 6.1 单元测试
- 各个管理器模块的独立测试
- 路由算法测试
- 聚合逻辑测试

### 6.2 集成测试
- 使用MCP Inspector进行端到端测试
- 使用Everything Server进行兼容性测试
- 多服务器场景测试

### 6.3 性能测试
- 并发请求处理能力
- 内存使用情况
- 连接管理效率

## 7. 部署和运维

### 7.1 部署方式
- **本地部署**: 直接运行Node.js进程
- **容器化**: Docker容器部署
- **服务化**: 作为系统服务运行

### 7.2 监控和日志
- **结构化日志**: 使用JSON格式日志
- **性能指标**: 请求延迟、成功率等
- **健康检查**: 后端服务器连接状态

## 8. 扩展性考虑

### 8.1 水平扩展
- 支持动态添加/移除后端服务器
- 负载均衡机制
- 服务发现集成

### 8.2 功能扩展
- ✅ HTTP传输支持 (SSE + Streamable HTTP)
- 认证和授权机制
- 缓存层实现
- 配置热重载
- WebSocket 传输支持

## 9. 安全考虑

### 9.1 访问控制
- 后端服务器访问权限控制
- 工具执行权限管理
- 资源访问限制

### 9.2 数据安全
- 敏感数据过滤
- 日志脱敏
- 传输加密

## 10. 总结

本设计方案实现了一个功能完整、架构清晰的MCP Server Proxy，具有以下特点：

- **完全兼容**: 严格遵循MCP协议规范
- **模块化设计**: 各组件职责清晰，易于维护
- **可扩展性**: 支持多种传输方式和扩展机制
- **健壮性**: 完善的错误处理和恢复机制
- **易用性**: 简单的配置和部署方式

该方案能够满足面试要求，展示了对MCP协议的深入理解和系统设计能力。
