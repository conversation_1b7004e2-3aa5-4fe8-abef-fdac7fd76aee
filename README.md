# MCP Server Proxy

一个强大的代理服务器，可以聚合多个 MCP (Model Context Protocol) 服务器，让 LLM 客户端通过单一连接访问来自多个后端服务器的工具、资源和提示模板。

## ✨ 核心特性

- **🔗 多服务器聚合**: 同时连接多个 MCP 服务器（已测试 25+ 工具）
- **🚀 多传输协议支持**:
  - **代理对外**: stdio、HTTP (SSE)、Streamable HTTP
  - **后端连接**: stdio、HTTP (SSE)、Streamable HTTP 混合连接
- **🏷️ 命名空间管理**: 通过可配置命名空间避免命名冲突
- **📡 实时通知转发**: 转发后端服务器的通知到客户端
- **✅ 协议完全兼容**: 完全兼容 MCP 规范和 SDK v1.17.5
- **🔧 会话管理**: HTTP 传输的高级会话处理
- **🛡️ 健壮错误处理**: 完善的错误处理和连接管理
- **📊 全面日志记录**: 可配置级别的详细日志

## 🏗️ 架构设计

### 基础架构
```
LLM Client (Cursor/Claude Desktop)
    ↓ (JSON-RPC 2.0 via stdio/HTTP/Streamable)
MCP Server Proxy (多传输协议支持)
    ↓ (Multiple connections via stdio/HTTP/Streamable)
Backend MCP Servers (Everything, Memory, SSE, Streamable HTTP, etc.)
```

### 多传输协议架构
```
┌─────────────────┐    ┌──────────────────────────────────┐
│   LLM Client    │    │        MCP Server Proxy          │
│                 │    │                                  │
│ ┌─────────────┐ │    │ ┌─────────────┐ ┌──────────────┐ │
│ │ stdio       │◄┼────┼►│ stdio       │ │ BackendMgr   │ │
│ │ HTTP (SSE)  │◄┼────┼►│ HTTP (SSE)  │ │ CapabilityAgg│ │
│ │ Streamable  │◄┼────┼►│ Streamable  │ │ RequestRouter│ │
│ └─────────────┘ │    │ └─────────────┘ │ NotifyMgr    │ │
└─────────────────┘    │                 └──────────────┘ │
                       └──────────────────────────────────┘
                                    │
                       ┌────────────┼────────────┐
                       ▼            ▼            ▼
              ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
              │Everything   │ │Simple SSE   │ │Streamable   │
              │Server       │ │Server       │ │HTTP Server  │
              │(stdio)      │ │(HTTP)       │ │(Streamable) │
              └─────────────┘ └─────────────┘ └─────────────┘
```

代理服务器对客户端表现为单一的 MCP 服务器，同时管理与多个后端服务器的连接。

## Installation

```bash
npm install
npm run build
```

## Usage

### Basic Usage

```bash
npm start
```

### Development Mode

```bash
npm run dev
```

### With Custom Configuration

```bash
CONFIG_FILE=./my-config.json npm start
```

### Environment Variables

- `LOG_LEVEL`: Set logging level (DEBUG, INFO, WARN, ERROR)
- `CONFIG_FILE`: Path to configuration file

## Configuration

Create a configuration file (JSON format) to define your backend servers:

```json
{
  "serverInfo": {
    "name": "mcp-server-proxy",
    "version": "1.0.0"
  },
  "servers": [
    {
      "id": "filesystem",
      "name": "Filesystem Server",
      "command": "npx",
      "args": ["@modelcontextprotocol/server-filesystem", "/tmp"],
      "transport": "stdio",
      "namespace": "fs",
      "enabled": true
    },
    {
      "id": "database",
      "name": "Database Server",
      "command": "node",
      "args": ["./database-server.js"],
      "transport": "stdio",
      "namespace": "db",
      "enabled": true,
      "env": {
        "DB_URL": "postgresql://localhost:5432/mydb"
      }
    }
  ]
}
```

### Configuration Options

- `id`: Unique identifier for the server
- `name`: Human-readable name
- `command`: Command to start the server
- `args`: Command arguments (optional)
- `transport`: Transport type ("stdio", "http", or "streamable")
- `namespace`: Namespace prefix for tools/resources/prompts (optional)
- `enabled`: Whether the server is enabled
- `env`: Environment variables (optional)
- `url`: HTTP URL (required for HTTP and streamable transports)

## Namespacing

The proxy supports namespacing to avoid conflicts between servers. For example:

- Without namespace: `list_files`, `query_database`
- With namespace: `fs.list_files`, `db.query_database`

## Testing

### Using MCP Inspector

```bash
# Install MCP Inspector
npm install -g @modelcontextprotocol/inspector

# Test the proxy
npx @modelcontextprotocol/inspector node dist/index.js
```

### Using Everything Server

```bash
# The everything server provides comprehensive test capabilities
npx @modelcontextprotocol/inspector npx @modelcontextprotocol/server-everything
```

## API

The proxy implements the full MCP specification:

### Lifecycle
- `initialize` - Initialize connection and negotiate capabilities
- `initialized` - Confirm initialization complete

### Tools
- `tools/list` - List all available tools from all servers
- `tools/call` - Execute a tool (routed to appropriate server)

### Resources  
- `resources/list` - List all available resources from all servers
- `resources/read` - Read a resource (routed to appropriate server)

### Prompts
- `prompts/list` - List all available prompts from all servers
- `prompts/get` - Get a prompt (routed to appropriate server)

### Notifications
- `notifications/tools/list_changed` - Tools list changed
- `notifications/resources/list_changed` - Resources list changed  
- `notifications/prompts/list_changed` - Prompts list changed

## Development

### Project Structure

```
src/
├── proxy/           # Main proxy server
├── managers/        # Component managers
│   ├── BackendManager.ts      # Backend server connections
│   ├── CapabilityAggregator.ts # Capability aggregation
│   ├── RequestRouter.ts       # Request routing
│   └── NotificationManager.ts # Notification handling
├── types/           # TypeScript type definitions
├── utils/           # Utility functions
└── config/          # Configuration management
```

### Building

```bash
npm run build
```

### Linting

```bash
npm run lint
```

### Testing

#### Unit Tests
```bash
npm test
```

#### Integration Testing with MCP Inspector

1. **Basic functionality test**:
```bash
# Test with simple configuration (no backend servers)
node dist/index.js examples/simple-config.json
```

2. **Test with MCP Inspector**:
```bash
npx @modelcontextprotocol/inspector node dist/index.js examples/simple-config.json
```

This will:
- Start the MCP Inspector on a local port
- Launch your proxy server as a subprocess
- Open a web interface for interactive testing

The web interface allows you to:
- View aggregated tools, resources, and prompts
- Test tool execution with custom parameters
- Monitor real-time JSON-RPC communication
- Debug protocol messages and errors
- Inspect server capabilities and metadata

#### Testing with Backend Servers

1. **Filesystem Server Test**:
```bash
npx @modelcontextprotocol/inspector node dist/index.js examples/config.json
```

2. **Everything Server Test**:
```bash
npx @modelcontextprotocol/inspector npx @modelcontextprotocol/server-everything
```

#### Verification Checklist

- ✅ Proxy starts without errors
- ✅ Configuration loads correctly
- ✅ MCP Inspector connects successfully
- ✅ Protocol handshake completes (initialize/initialized)
- ✅ Tools/resources/prompts are aggregated properly
- ✅ Namespace prefixes work correctly
- ✅ Request routing functions properly
- ✅ Error handling is robust
- ✅ Notifications are forwarded correctly

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Troubleshooting

### Common Issues

1. **Backend server fails to start**
   - Check command and arguments in configuration
   - Verify server executable is available
   - Check environment variables

2. **Tools not appearing**
   - Verify server is connected (check logs)
   - Check namespace configuration
   - Ensure server supports the tools capability

3. **Permission errors**
   - Check file system permissions
   - Verify environment variables are set correctly

### Debug Mode

Enable debug logging:

```bash
LOG_LEVEL=DEBUG npm start
```

This will show detailed information about:
- Server connections
- Capability loading
- Request routing
- Notification handling
