/**
 * MCP Server Proxy - Main proxy server implementation
 */
import { EventEmitter } from 'events';
import { ProxyConfig } from '../types/index.js';
/**
 * Main MCP Server Proxy class
 */
export declare class MCPServerProxy extends EventEmitter {
    private server;
    private backendManager;
    private capabilityAggregator;
    private requestRouter;
    private notificationManager;
    private config;
    private state;
    constructor(config: ProxyConfig);
    /**
     * Set up event handlers between managers
     */
    private setupEventHandlers;
    /**
     * Set up MCP server handlers using the low-level API
     */
    private setupServerHandlers;
    /**
     * Set up all MCP request handlers
     */
    private setupRequestHandlers;
    /**
     * Get dynamic capabilities based on current backend connections
     */
    private getDynamicCapabilities;
    /**
     * Update aggregated capabilities from all backend connections
     */
    private updateAggregatedCapabilities;
    /**
     * Update server capabilities registration
     */
    private updateServerCapabilities;
    /**
     * Set up dynamic handlers based on aggregated capabilities
     */
    private setupDynamicHandlers;
    /**
     * Convert MCP prompt arguments to Zod schema
     */
    private convertArgsToSchema;
    /**
     * Send capability change notifications
     */
    private sendCapabilityChangeNotifications;
    /**
     * Forward notification to clients
     */
    private forwardNotification;
    /**
     * Start the proxy server
     */
    start(): Promise<void>;
    /**
     * Stop the proxy server
     */
    stop(): Promise<void>;
    /**
     * Get proxy status
     */
    getStatus(): any;
}
//# sourceMappingURL=MCPServerProxy.d.ts.map