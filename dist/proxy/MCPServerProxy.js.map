{"version": 3, "file": "MCPServerProxy.js", "sourceRoot": "", "sources": ["../../src/proxy/MCPServerProxy.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,2CAA2C,CAAC;AACnE,OAAO,EAAE,oBAAoB,EAAE,MAAM,2CAA2C,CAAC;AACjF,OAAO,EACL,sBAAsB,EACtB,qBAAqB,EACrB,0BAA0B,EAC1B,kCAAkC,EAClC,yBAAyB,EACzB,wBAAwB,EACxB,sBAAsB,EACvB,MAAM,oCAAoC,CAAC;AAC5C,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AAExB,OAAO,EAAE,cAAc,EAAE,MAAM,+BAA+B,CAAC;AAC/D,OAAO,EAAE,oBAAoB,EAAE,MAAM,qCAAqC,CAAC;AAC3E,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAC;AAC7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,oCAAoC,CAAC;AAEzE,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAU5C;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,YAAY;IACtC,MAAM,CAAS;IACf,cAAc,CAAiB;IAC/B,oBAAoB,CAAuB;IAC3C,aAAa,CAAgB;IAC7B,mBAAmB,CAAsB;IACzC,MAAM,CAAc;IACpB,KAAK,CAAa;IAE1B,YAAY,MAAmB;QAC7B,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG;YACX,WAAW,EAAE,KAAK;SACnB,CAAC;QAEF,iDAAiD;QACjD,uEAAuE;QACvE,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CACtB;YACE,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC,IAAI;YAC5B,OAAO,EAAE,MAAM,CAAC,UAAU,CAAC,OAAO;SACnC,EACD;YACE,YAAY,EAAE;gBACZ,KAAK,EAAE;oBACL,WAAW,EAAE,IAAI;iBAClB;gBACD,SAAS,EAAE;oBACT,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,IAAI;iBAClB;gBACD,OAAO,EAAE;oBACP,WAAW,EAAE,IAAI;iBAClB;aACF;SACF,CACF,CAAC;QAEF,sBAAsB;QACtB,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC;QAC3C,IAAI,CAAC,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC;QACvD,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACvF,IAAI,CAAC,mBAAmB,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAExE,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,yBAAyB;QACzB,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,QAAgB,EAAE,EAAE;YAC9D,MAAM,CAAC,IAAI,CAAC,6BAA6B,QAAQ,EAAE,CAAC,CAAC;YACrD,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC,QAAgB,EAAE,EAAE;YACjE,MAAM,CAAC,IAAI,CAAC,gCAAgC,QAAQ,EAAE,CAAC,CAAC;YACxD,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,sBAAsB,EAAE,CAAC,QAAgB,EAAE,EAAE;YAClE,MAAM,CAAC,KAAK,CAAC,oCAAoC,QAAQ,EAAE,CAAC,CAAC;YAC7D,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,+BAA+B;QAC/B,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,qBAAqB,EAAE,GAAG,EAAE;YACvD,MAAM,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;YACxD,yDAAyD;YACzD,IAAI,CAAC,iCAAiC,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,8BAA8B;QAC9B,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,YAAiB,EAAE,EAAE;YAChE,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,2CAA2C;QAC3C,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,iBAAiB;QACjB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,CAAC;YACjE,OAAO,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;YACrE,8CAA8C;YAC9C,MAAM,cAAc,GAAG;gBACrB,OAAO,EAAE,KAAc;gBACvB,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC9B,MAAM,EAAE,YAAY;gBACpB,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC;YACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YACvE,OAAO,QAAQ,CAAC,MAAM,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,CAAC;YACjE,OAAO,EAAE,SAAS,EAAE,YAAY,CAAC,SAAS,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAC3E,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,CAAC;YACjE,OAAO,EAAE,iBAAiB,EAAE,YAAY,CAAC,iBAAiB,IAAI,EAAE,EAAE,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yBAAyB,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;YACzE,8CAA8C;YAC9C,MAAM,cAAc,GAAG;gBACrB,OAAO,EAAE,KAAc;gBACvB,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC9B,MAAM,EAAE,gBAAgB;gBACxB,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC;YACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YACvE,OAAO,QAAQ,CAAC,MAAM,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,mBAAmB;QACnB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,CAAC;YACjE,OAAO,EAAE,OAAO,EAAE,YAAY,CAAC,OAAO,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;YACtE,8CAA8C;YAC9C,MAAM,cAAc,GAAG;gBACrB,OAAO,EAAE,KAAc;gBACvB,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC9B,MAAM,EAAE,aAAa;gBACrB,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC;YACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YACvE,OAAO,QAAQ,CAAC,MAAM,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,MAAM,sBAAsB,GAAG,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,CAAC;QAE3E,OAAO;YACL,KAAK,EAAE,sBAAsB,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS;YAC/D,SAAS,EAAE,sBAAsB,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS;YACvE,OAAO,EAAE,sBAAsB,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS;SACpE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,4BAA4B;QAClC,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;QAC5D,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAE1D,+CAA+C;QAC/C,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,wBAAwB;QAC9B,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAEnD,2CAA2C;YAC3C,sFAAsF;YACtF,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,YAAY,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,+DAA+D;QAC/D,kEAAkE;QAClE,MAAM,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,IAAW;QACrC,MAAM,MAAM,GAA8B,EAAE,CAAC;QAC7C,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,mEAAmE;YACnE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC3C,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iCAAiC;QAC7C,IAAI,CAAC;YACH,mDAAmD;YACnD,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,mEAAmE;YACnE,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,YAAiB;QACjD,IAAI,CAAC;YACH,kDAAkD;YAClD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC;YAExC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,CAAC,IAAI,CAAC,wCAAwC,EAAE,YAAY,CAAC,CAAC;gBACpE,OAAO;YACT,CAAC;YAED,4DAA4D;YAC5D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;gBAC5B,MAAM,CAAC,KAAK,CAAC,qDAAqD,EAAE,MAAM,CAAC,CAAC;gBAC5E,OAAO;YACT,CAAC;YAED,2DAA2D;YAC3D,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,MAAM,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;gBACtE,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;YACvD,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAE5C,6BAA6B;YAC7B,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC/C,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;oBACzB,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBACpD,CAAC;YACH,CAAC;YAED,+BAA+B;YAC/B,MAAM,SAAS,GAAG,IAAI,oBAAoB,EAAE,CAAC;YAC7C,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAErC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;YAE9B,+DAA+D;YAC/D,uEAAuE;YAEvE,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YAErD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAE5C,kCAAkC;YAClC,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;YAE1C,eAAe;YACf,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAC5B,CAAC;YAED,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAExC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACnE,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;aACvB,CAAC,CAAC;YACH,YAAY,EAAE,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE;SAC1D,CAAC;IACJ,CAAC;CACF"}