{"version": 3, "file": "MCPServerProxy.d.ts", "sourceRoot": "", "sources": ["../../src/proxy/MCPServerProxy.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAiBtC,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAWhD;;GAEG;AACH,qBAAa,cAAe,SAAQ,YAAY;IAC9C,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,cAAc,CAAiB;IACvC,OAAO,CAAC,oBAAoB,CAAuB;IACnD,OAAO,CAAC,aAAa,CAAgB;IACrC,OAAO,CAAC,mBAAmB,CAAsB;IACjD,OAAO,CAAC,MAAM,CAAc;IAC5B,OAAO,CAAC,KAAK,CAAa;IAC1B,OAAO,CAAC,yBAAyB,CAA+B;IAChE,OAAO,CAAC,oBAAoB,CAAqB;gBAErC,MAAM,EAAE,WAAW;IAyC/B;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAoC1B;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAK3B;;OAEG;IACH,OAAO,CAAC,oBAAoB;IA+D5B;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAU9B;;OAEG;IACH,OAAO,CAAC,4BAA4B;IAQpC;;OAEG;IACH,OAAO,CAAC,wBAAwB;IAYhC;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAQ5B;;OAEG;YACW,iCAAiC;IAgB/C;;OAEG;YACW,0BAA0B;IA4DxC;;OAEG;YACW,mBAAmB;IA4BjC;;OAEG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IA6B5B;;OAEG;IACG,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;IA+B3B;;OAEG;IACH,SAAS,IAAI,GAAG;CAWjB"}