{"version": 3, "file": "BackendManager.js", "sourceRoot": "", "sources": ["../../src/managers/BackendManager.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,KAAK,EAAgB,MAAM,eAAe,CAAC;AACpD,OAAO,EAAE,MAAM,EAAE,MAAM,2CAA2C,CAAC;AACnE,OAAO,EAAE,oBAAoB,EAAE,MAAM,2CAA2C,CAAC;AACjF,OAAO,EAAE,kBAAkB,EAAE,MAAM,yCAAyC,CAAC;AAC7E,oEAAoE;AACpE,oKAAoK;AACpK,OAAO,EAAE,6BAA6B,EAAE,MAAM,oDAAoD,CAAC;AAEnG,0CAA0C;AAC1C,OAAO,EAAE,WAAW,IAAI,mBAAmB,EAAE,MAAM,aAAa,CAAC;AAEjE,6CAA6C;AAC7C,IAAI,OAAO,UAAU,CAAC,WAAW,KAAK,WAAW,EAAE,CAAC;IACjD,UAAkB,CAAC,WAAW,GAAG,mBAAmB,CAAC;AACxD,CAAC;AACD,OAAO,EACL,iCAAiC,EACjC,qCAAqC,EACrC,mCAAmC,EACnC,qBAAqB,EACrB,yBAAyB,EACzB,iCAAiC,EACjC,uBAAuB,EACxB,MAAM,oCAAoC,CAAC;AAM5C,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAE5C,MAAM,OAAO,cAAe,SAAQ,YAAY;IACtC,WAAW,GAAG,IAAI,GAAG,EAA6B,CAAC;IAE3D;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,MAA2B;QACzC,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,UAAU,MAAM,CAAC,EAAE,iBAAiB,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,0BAA0B,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;QAEpE,MAAM,UAAU,GAAsB;YACpC,EAAE,EAAE,MAAM,CAAC,EAAE;YACb,MAAM;YACN,MAAM,EAAE,IAAI;YACZ,SAAS,EAAE,KAAK;YAChB,kBAAkB,EAAE,KAAK;YACzB,KAAK,EAAE,EAAE;YACT,SAAS,EAAE,EAAE;YACb,iBAAiB,EAAE,EAAE;YACrB,OAAO,EAAE,EAAE;SACZ,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAE5C,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,QAAgB;QAClC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,UAAU,QAAQ,YAAY,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,UAAU,QAAQ,uBAAuB,CAAC,CAAC;YACvD,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,yBAAyB,QAAQ,EAAE,CAAC,CAAC;YAEjD,IAAI,UAAU,CAAC,MAAM,CAAC,SAAS,KAAK,OAAO,EAAE,CAAC;gBAC5C,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAC5C,CAAC;iBAAM,IAAI,UAAU,CAAC,MAAM,CAAC,SAAS,KAAK,MAAM,EAAE,CAAC;gBAClD,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAC3C,CAAC;iBAAM,IAAI,UAAU,CAAC,MAAM,CAAC,SAAS,KAAK,YAAY,EAAE,CAAC;gBACxD,MAAM,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;YACrD,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,0BAA0B,UAAU,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YAC3E,CAAC;YAED,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC;YAC5B,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;YAEjC,4BAA4B;YAC5B,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;YAE9C,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,qCAAqC,QAAQ,EAAE,CAAC,CAAC;QAE/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,UAAU,CAAC,SAAS,GAAG,KAAc,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,+BAA+B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAChE,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,QAAQ,EAAE,KAAc,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,UAA6B;QAC5D,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC;QAE9B,2BAA2B;QAC3B,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE,EAAE;YAC7D,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE;YACtC,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;SAChC,CAAC,CAAC;QAEH,8BAA8B;QAC9B,MAAM,SAAS,GAAG,IAAI,oBAAoB,CAAC;YACzC,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE;SACxB,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;YACE,IAAI,EAAE,oBAAoB,MAAM,CAAC,EAAE,EAAE;YACrC,OAAO,EAAE,OAAO;SACjB,EACD;YACE,YAAY,EAAE,EAAE;SACjB,CACF,CAAC;QAEF,+BAA+B;QAC/B,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YAClC,MAAM,CAAC,KAAK,CAAC,4BAA4B,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9D,UAAU,CAAC,SAAS,GAAG,KAAK,CAAC;YAC7B,UAAU,CAAC,SAAS,GAAG,KAAK,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;YACxC,MAAM,CAAC,IAAI,CAAC,6BAA6B,MAAM,CAAC,EAAE,UAAU,IAAI,YAAY,MAAM,EAAE,CAAC,CAAC;YACtF,UAAU,CAAC,SAAS,GAAG,KAAK,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEhC,mBAAmB;QACnB,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;QAC1B,UAAkB,CAAC,OAAO,GAAG,aAAa,CAAC;QAE5C,+BAA+B;QAC/B,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,UAA6B;QAC3D,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC;QAE9B,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,4DAA4D,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3F,CAAC;QAED,uBAAuB;QACvB,MAAM,SAAS,GAAG,IAAI,kBAAkB,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;QAE9D,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;YACE,IAAI,EAAE,oBAAoB,MAAM,CAAC,EAAE,EAAE;YACrC,OAAO,EAAE,OAAO;SACjB,EACD;YACE,YAAY,EAAE,EAAE;SACjB,CACF,CAAC;QAEF,qBAAqB;QACrB,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEhC,mBAAmB;QACnB,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;QAE3B,+BAA+B;QAC/B,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,2BAA2B,CAAC,UAA6B;QACrE,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC;QAE9B,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,uEAAuE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;QACtG,CAAC;QAED,mCAAmC;QACnC,MAAM,SAAS,GAAG,IAAI,6BAA6B,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;QAEzE,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB;YACE,IAAI,EAAE,oBAAoB,MAAM,CAAC,EAAE,EAAE;YACrC,OAAO,EAAE,OAAO;SACjB,EACD;YACE,YAAY,EAAE,EAAE;SACjB,CACF,CAAC;QAEF,qBAAqB;QACrB,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEhC,mBAAmB;QACnB,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;QAE3B,+BAA+B;QAC/B,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,UAA6B;QAChE,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC;QAE9B,IAAI,CAAC;YACH,aAAa;YACb,IAAI,CAAC;gBACH,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,OAAO,CACxC,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,EAAE,EAAE,EACpC,qBAAqB,CACtB,CAAC;gBACF,UAAU,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC;YAC/C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,4BAA4B,UAAU,CAAC,EAAE,4BAA4B,CAAC,CAAC;gBACnF,UAAU,CAAC,KAAK,GAAG,EAAE,CAAC;YACxB,CAAC;YAED,iBAAiB;YACjB,IAAI,CAAC;gBACH,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,OAAO,CAC5C,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,EAAE,EAAE,EACxC,yBAAyB,CAC1B,CAAC;gBACF,UAAU,CAAC,SAAS,GAAG,iBAAiB,CAAC,SAAS,IAAI,EAAE,CAAC;YAC3D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,gCAAgC,UAAU,CAAC,EAAE,gCAAgC,CAAC,CAAC;gBAC3F,UAAU,CAAC,SAAS,GAAG,EAAE,CAAC;YAC5B,CAAC;YAED,0BAA0B;YAC1B,IAAI,CAAC;gBACH,MAAM,yBAAyB,GAAG,MAAM,MAAM,CAAC,OAAO,CACpD,EAAE,MAAM,EAAE,0BAA0B,EAAE,MAAM,EAAE,EAAE,EAAE,EAClD,iCAAiC,CAClC,CAAC;gBACF,UAAU,CAAC,iBAAiB,GAAG,yBAAyB,CAAC,iBAAiB,IAAI,EAAE,CAAC;YACnF,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,yCAAyC,UAAU,CAAC,EAAE,yCAAyC,CAAC,CAAC;gBAC7G,UAAU,CAAC,iBAAiB,GAAG,EAAE,CAAC;YACpC,CAAC;YAED,eAAe;YACf,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,OAAO,CAC1C,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,EAAE,EACtC,uBAAuB,CACxB,CAAC;gBACF,UAAU,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,IAAI,EAAE,CAAC;YACrD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,8BAA8B,UAAU,CAAC,EAAE,8BAA8B,CAAC,CAAC;gBACvF,UAAU,CAAC,OAAO,GAAG,EAAE,CAAC;YAC1B,CAAC;YAED,8BAA8B;YAC9B,UAAU,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAErC,MAAM,CAAC,KAAK,CAAC,2BAA2B,UAAU,CAAC,EAAE,GAAG,EAAE;gBACxD,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,MAAM;gBAC9B,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,MAAM;gBACtC,iBAAiB,EAAE,UAAU,CAAC,iBAAiB,CAAC,MAAM;gBACtD,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,MAAM;aACnC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC;QAEnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,mCAAmC,UAAU,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACzE,kEAAkE;YAClE,UAAU,CAAC,kBAAkB,GAAG,IAAI,CAAC;YACrC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,UAA6B;QAC7D,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,UAAU,CAAC;QAElC,2BAA2B;QAC3B,MAAM,CAAC,sBAAsB,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC1E,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,EAAE,CAAC,CAAC;YACtD,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CACnC,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,EAAE,EAAE,EACpC,qBAAqB,CACtB,CAAC;gBACF,UAAU,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC;gBACxC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;YACxC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,+BAA+B;QAC/B,MAAM,CAAC,sBAAsB,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YAC9E,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,EAAE,CAAC,CAAC;YAC1D,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CACnC,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,EAAE,EAAE,EACxC,yBAAyB,CAC1B,CAAC;gBACF,UAAU,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,IAAI,EAAE,CAAC;gBAChD,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;YACxC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAChE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,6BAA6B;QAC7B,MAAM,CAAC,sBAAsB,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YAC5E,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,EAAE,CAAC,CAAC;YACxD,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CACnC,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,EAAE,EACtC,uBAAuB,CACxB,CAAC;gBACF,UAAU,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,IAAI,EAAE,CAAC;gBAC5C,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;YACxC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,QAAgB;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QACrC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YACzC,OAAO;QACT,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,8BAA8B,QAAQ,EAAE,CAAC,CAAC;QAEtD,IAAI,CAAC;YACH,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;gBACtB,MAAM,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAClC,CAAC;YAED,4BAA4B;YAC5B,MAAM,OAAO,GAAI,UAAkB,CAAC,OAAuB,CAAC;YAC5D,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBAC/B,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,4BAA4B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC;gBAAS,CAAC;YACT,UAAU,CAAC,SAAS,GAAG,KAAK,CAAC;YAC7B,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACjB,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;aAC3D,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEpD,MAAM,OAAO,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC;IAC/C,CAAC;CACF"}