{"version": 3, "file": "RequestRouter.js", "sourceRoot": "", "sources": ["../../src/managers/RequestRouter.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAIL,oBAAoB,EACpB,wBAAwB,EACxB,qBAAqB,EACtB,MAAM,oCAAoC,CAAC;AAC5C,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AAQxB,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAE5C,MAAM,OAAO,aAAa;IAEd;IACA;IAFV,YACU,cAA8B,EAC9B,oBAA0C;QAD1C,mBAAc,GAAd,cAAc,CAAgB;QAC9B,yBAAoB,GAApB,oBAAoB,CAAsB;IACjD,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,OAAuB;QACxC,MAAM,CAAC,KAAK,CAAC,oBAAoB,OAAO,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QAEvE,IAAI,CAAC;YACH,iCAAiC;YACjC,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC;gBACvB,KAAK,YAAY;oBACf,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;gBAEvC,KAAK,YAAY;oBACf,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;gBAE7C,KAAK,gBAAgB;oBACnB,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;gBAE3C,KAAK,gBAAgB;oBACnB,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;gBAEjD,KAAK,cAAc;oBACjB,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBAEzC,KAAK,aAAa;oBAChB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;gBAE9C;oBACE,8DAA8D;oBAC9D,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,yBAAyB,OAAO,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YAEhE,MAAM,IAAI,KAAK,CAAC,mBAAmB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,OAAuB;QAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,CAAC;QAEjE,OAAO;YACL,OAAO,EAAE,KAAK;YACd,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,MAAM,EAAE;gBACN,KAAK,EAAE,YAAY,CAAC,KAAK;aAC1B;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,OAAuB;QACnD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAa,CAAC;QACrC,MAAM,QAAQ,GAAG,MAAM,EAAE,IAAI,CAAC;QAE9B,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,mCAAmC,CAAC,CAAC;QAC3F,CAAC;QAED,0BAA0B;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACnE,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,mBAAmB,QAAQ,EAAE,CAAC,CAAC;QACrF,CAAC;QAED,yBAAyB;QACzB,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACzE,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,iCAAiC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7G,CAAC;QAED,yCAAyC;QACzC,MAAM,cAAc,GAAmB;YACrC,GAAG,OAAO;YACV,MAAM,EAAE;gBACN,GAAG,MAAM;gBACT,IAAI,EAAE,SAAS,CAAC,YAAY;aAC7B;SACF,CAAC;QAEF,4BAA4B;QAC5B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,OAAO,CAC9C;gBACE,MAAM,EAAE,YAAY;gBACpB,MAAM,EAAE;oBACN,GAAG,MAAM;oBACT,IAAI,EAAE,SAAS,CAAC,YAAY;iBAC7B;aACF,EACD,oBAAoB,CACrB,CAAC;YACF,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,MAAM,EAAE,QAAQ;aACjB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,wBAAwB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,0BAA0B,KAAK,EAAE,CAAC,CAAC;QACzF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAAuB;QACjD,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,CAAC;QAEjE,OAAO;YACL,OAAO,EAAE,KAAK;YACd,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,MAAM,EAAE;gBACN,SAAS,EAAE,YAAY,CAAC,SAAS;aAClC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,OAAuB;QACvD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAa,CAAC;QACrC,MAAM,WAAW,GAAG,MAAM,EAAE,GAAG,CAAC;QAEhC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,sCAAsC,CAAC,CAAC;QAC9F,CAAC;QAED,0EAA0E;QAC1E,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC3D,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,6BAA6B,CAAC,CAAC;QACrF,CAAC;QAED,0BAA0B;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QACvE,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,uBAAuB,YAAY,EAAE,CAAC,CAAC;QAC7F,CAAC;QAED,yBAAyB;QACzB,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACzE,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,iCAAiC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7G,CAAC;QAED,6CAA6C;QAC7C,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,YAAY,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;QAC9E,MAAM,cAAc,GAAmB;YACrC,GAAG,OAAO;YACV,MAAM,EAAE;gBACN,GAAG,MAAM;gBACT,GAAG,EAAE,WAAW;aACjB;SACF,CAAC;QAEF,4BAA4B;QAC5B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,OAAO,CAC9C;gBACE,MAAM,EAAE,gBAAgB;gBACxB,MAAM,EAAE;oBACN,GAAG,MAAM;oBACT,GAAG,EAAE,WAAW;iBACjB;aACF,EACD,wBAAwB,CACzB,CAAC;YACF,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,MAAM,EAAE,QAAQ;aACjB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,4BAA4B,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,yBAAyB,KAAK,EAAE,CAAC,CAAC;QACxF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,OAAuB;QAC/C,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,CAAC;QAEjE,OAAO;YACL,OAAO,EAAE,KAAK;YACd,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,MAAM,EAAE;gBACN,OAAO,EAAE,YAAY,CAAC,OAAO;aAC9B;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,OAAuB;QACpD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAa,CAAC;QACrC,MAAM,UAAU,GAAG,MAAM,EAAE,IAAI,CAAC;QAEhC,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,qCAAqC,CAAC,CAAC;QAC7F,CAAC;QAED,0BAA0B;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QACrE,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,qBAAqB,UAAU,EAAE,CAAC,CAAC;QACzF,CAAC;QAED,yBAAyB;QACzB,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACzE,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,iCAAiC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7G,CAAC;QAED,2CAA2C;QAC3C,MAAM,cAAc,GAAmB;YACrC,GAAG,OAAO;YACV,MAAM,EAAE;gBACN,GAAG,MAAM;gBACT,IAAI,EAAE,SAAS,CAAC,YAAY;aAC7B;SACF,CAAC;QAEF,4BAA4B;QAC5B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,OAAO,CAC9C;gBACE,MAAM,EAAE,aAAa;gBACrB,MAAM,EAAE;oBACN,GAAG,MAAM;oBACT,IAAI,EAAE,SAAS,CAAC,YAAY;iBAC7B;aACF,EACD,qBAAqB,CACtB,CAAC;YACF,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,MAAM,EAAE,QAAQ;aACjB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,yBAAyB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,sBAAsB,KAAK,EAAE,CAAC,CAAC;QACrF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,OAAuB;QACxD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;QAC5D,MAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEpE,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,8BAA8B,CAAC,CAAC;QACtF,CAAC;QAED,6BAA6B;QAC7B,MAAM,UAAU,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAEvC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,OAAO,CAC9C;gBACE,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,EACD,CAAC,CAAC,GAAG,EAAE,CAAC,sCAAsC;aAC/C,CAAC;YACF,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,MAAM,EAAE,QAAQ;aACjB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,qBAAqB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAC7F,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,GAAW;QACrC,+BAA+B;QAC/B,+EAA+E;QAE/E,sDAAsD;QACtD,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,CAAC;QAEjE,wCAAwC;QACxC,KAAK,MAAM,QAAQ,IAAI,YAAY,CAAC,SAAS,EAAE,CAAC;YAC9C,IAAI,QAAQ,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;gBACzB,OAAO,QAAQ,CAAC,IAAI,CAAC;YACvB,CAAC;QACH,CAAC;QAED,kCAAkC;QAClC,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAC3C,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,EAAO,EAAE,IAAY,EAAE,OAAe;QAChE,OAAO;YACL,OAAO,EAAE,KAAK;YACd,EAAE;YACF,KAAK,EAAE;gBACL,IAAI;gBACJ,OAAO;aACR;SACF,CAAC;IACJ,CAAC;CACF"}