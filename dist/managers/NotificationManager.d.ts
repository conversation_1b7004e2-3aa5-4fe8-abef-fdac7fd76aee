/**
 * Notification Manager - Handles notifications from backend servers
 */
import { EventEmitter } from 'events';
import { JSONRPCNotification } from '@modelcontextprotocol/sdk/types.js';
import { BackendManager } from './BackendManager.js';
export declare class NotificationManager extends EventEmitter {
    private backendManager;
    private pendingNotifications;
    private notificationDebounceMs;
    private enabled;
    constructor(backendManager: BackendManager);
    /**
     * Enable notification forwarding
     */
    enable(): void;
    /**
     * Disable notification forwarding
     */
    disable(): void;
    /**
     * Set up listeners for backend manager events
     */
    private setupBackendListeners;
    /**
     * Handle capabilities updated event
     */
    private handleCapabilitiesUpdated;
    /**
     * Debounce notifications to prevent spam
     */
    private debounceNotification;
    /**
     * Emit a notification to the client
     */
    private emitNotification;
    /**
     * Handle a notification from a backend server
     */
    handleBackendNotification(serverId: string, notification: JSONRPCNotification): void;
    /**
     * Send a notification to all backend servers
     */
    broadcastToBackends(notification: JSONRPCNotification): Promise<void>;
    /**
     * Set notification debounce time
     */
    setDebounceTime(ms: number): void;
}
//# sourceMappingURL=NotificationManager.d.ts.map