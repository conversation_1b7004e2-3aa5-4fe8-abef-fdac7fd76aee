{"version": 3, "file": "NotificationManager.js", "sourceRoot": "", "sources": ["../../src/managers/NotificationManager.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAGtC,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAE5C,MAAM,OAAO,mBAAoB,SAAQ,YAAY;IAK/B;IAJZ,oBAAoB,GAAG,IAAI,GAAG,EAAU,CAAC;IACzC,sBAAsB,GAAG,GAAG,CAAC,CAAC,yBAAyB;IACvD,OAAO,GAAG,KAAK,CAAC,CAAC,uCAAuC;IAEhE,YAAoB,cAA8B;QAChD,KAAK,EAAE,CAAC;QADU,mBAAc,GAAd,cAAc,CAAgB;QAEhD,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,MAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,sBAAsB,EAAE,CAAC,QAAgB,EAAE,EAAE;YAClE,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,QAAgB,EAAE,EAAE;YAC9D,MAAM,CAAC,KAAK,CAAC,qBAAqB,QAAQ,EAAE,CAAC,CAAC;YAC9C,sDAAsD;QACxD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC,QAAgB,EAAE,KAAa,EAAE,EAAE;YAChF,MAAM,CAAC,KAAK,CAAC,wBAAwB,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACxF,wDAAwD;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,QAAgB;QAChD,MAAM,CAAC,KAAK,CAAC,oCAAoC,QAAQ,cAAc,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QAEvF,yEAAyE;QACzE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,CAAC,KAAK,CAAC,4EAA4E,CAAC,CAAC;YAC3F,OAAO;QACT,CAAC;QAED,8CAA8C;QAC9C,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,UAAU;YAAE,OAAO;QAExB,uCAAuC;QACvC,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,KAAK,CAAC,oDAAoD,CAAC,CAAC;YACnE,IAAI,CAAC,gBAAgB,CAAC;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,kCAAkC;aAC3C,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,CAAC,wBAAwB,EAAE,GAAG,EAAE;YACvD,MAAM,CAAC,KAAK,CAAC,wDAAwD,CAAC,CAAC;YACvE,IAAI,CAAC,gBAAgB,CAAC;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,sCAAsC;aAC/C,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,GAAG,EAAE;YACrD,MAAM,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;YACrE,IAAI,CAAC,gBAAgB,CAAC;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,oCAAoC;aAC7C,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,GAAW,EAAE,QAAoB;QAC5D,IAAI,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YACvC,OAAO,CAAC,kBAAkB;QAC5B,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEnC,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACtC,kEAAkE;YAClE,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,QAAQ,EAAE,CAAC;YACb,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,KAAK,CAAC,gBAAgB,GAAG,gCAAgC,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,YAAiC;QACxD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,CAAC,KAAK,CAAC,+CAA+C,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;YACnF,OAAO;QACT,CAAC;QAED,MAAM,CAAC,KAAK,CAAC,0BAA0B,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;QAC9D,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,yBAAyB,CAAC,QAAgB,EAAE,YAAiC;QAC3E,MAAM,CAAC,KAAK,CAAC,8BAA8B,QAAQ,KAAK,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;QAE/E,yCAAyC;QACzC,QAAQ,YAAY,CAAC,MAAM,EAAE,CAAC;YAC5B,KAAK,kCAAkC,CAAC;YACxC,KAAK,sCAAsC,CAAC;YAC5C,KAAK,oCAAoC;gBACvC,0DAA0D;gBAC1D,MAAM;YAER,KAAK,wBAAwB;gBAC3B,0CAA0C;gBAC1C,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;gBACpC,MAAM;YAER;gBACE,gCAAgC;gBAChC,MAAM,CAAC,KAAK,CAAC,oCAAoC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;gBACxE,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;gBACpC,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,YAAiC;QACzD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;QAC5D,MAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEpE,MAAM,CAAC,KAAK,CAAC,gCAAgC,gBAAgB,CAAC,MAAM,aAAa,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;QAExG,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;YACzD,IAAI,CAAC;gBACH,MAAM,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;YACzD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,kCAAkC,UAAU,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,EAAU;QACxB,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC;IACnC,CAAC;CACF"}