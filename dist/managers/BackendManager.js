/**
 * Backend Manager - Manages connections to multiple MCP servers
 */
import { EventEmitter } from 'events';
import { spawn } from 'child_process';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
// Import from server-everything package which has newer SDK version
// import { StreamableHTTPClientTransport } from '@modelcontextprotocol/server-everything/node_modules/@modelcontextprotocol/sdk/dist/esm/client/streamableHttp.js';
import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";
// Import EventSource polyfill for Node.js
import { EventSource as EventSourcePolyfill } from 'eventsource';
// Set up EventSource for Node.js environment
if (typeof globalThis.EventSource === 'undefined') {
    globalThis.EventSource = EventSourcePolyfill;
}
import { ToolListChangedNotificationSchema, ResourceListChangedNotificationSchema, PromptListChangedNotificationSchema, ListToolsResultSchema, ListResourcesResultSchema, ListResourceTemplatesResultSchema, ListPromptsResultSchema } from '@modelcontextprotocol/sdk/types.js';
import { logger } from '../utils/logger.js';
export class BackendManager extends EventEmitter {
    connections = new Map();
    /**
     * Add a backend server configuration
     */
    async addServer(config) {
        if (this.connections.has(config.id)) {
            throw new Error(`Server ${config.id} already exists`);
        }
        logger.info(`Adding backend server: ${config.id} (${config.name})`);
        const connection = {
            id: config.id,
            config,
            client: null,
            connected: false,
            capabilitiesLoaded: false,
            tools: [],
            resources: [],
            resourceTemplates: [],
            prompts: []
        };
        this.connections.set(config.id, connection);
        if (config.enabled) {
            await this.connectServer(config.id);
        }
    }
    /**
     * Connect to a backend server
     */
    async connectServer(serverId) {
        const connection = this.connections.get(serverId);
        if (!connection) {
            throw new Error(`Server ${serverId} not found`);
        }
        if (connection.connected) {
            logger.warn(`Server ${serverId} is already connected`);
            return;
        }
        try {
            logger.info(`Connecting to server: ${serverId}`);
            if (connection.config.transport === 'stdio') {
                await this.connectStdioServer(connection);
            }
            else if (connection.config.transport === 'http') {
                await this.connectHttpServer(connection);
            }
            else if (connection.config.transport === 'streamable') {
                await this.connectStreamableHttpServer(connection);
            }
            else {
                throw new Error(`Unsupported transport: ${connection.config.transport}`);
            }
            connection.connected = true;
            connection.lastError = undefined;
            // Load initial capabilities
            await this.loadServerCapabilities(connection);
            this.emit('server-connected', serverId);
            logger.info(`Successfully connected to server: ${serverId}`);
        }
        catch (error) {
            connection.lastError = error;
            logger.error(`Failed to connect to server ${serverId}:`, error);
            this.emit('server-disconnected', serverId, error);
            throw error;
        }
    }
    /**
     * Connect to a stdio-based MCP server
     */
    async connectStdioServer(connection) {
        const { config } = connection;
        // Spawn the server process
        const serverProcess = spawn(config.command, config.args || [], {
            env: { ...process.env, ...config.env },
            stdio: ['pipe', 'pipe', 'pipe']
        });
        // Create transport and client
        const transport = new StdioClientTransport({
            command: config.command,
            args: config.args || []
        });
        const client = new Client({
            name: `mcp-proxy-client-${config.id}`,
            version: '1.0.0'
        }, {
            capabilities: {}
        });
        // Handle server process events
        serverProcess.on('error', (error) => {
            logger.error(`Server process error for ${config.id}:`, error);
            connection.connected = false;
            connection.lastError = error;
            this.emit('server-disconnected', config.id, error);
        });
        serverProcess.on('exit', (code, signal) => {
            logger.warn(`Server process exited for ${config.id}: code=${code}, signal=${signal}`);
            connection.connected = false;
            this.emit('server-disconnected', config.id);
        });
        // Connect the client
        await client.connect(transport);
        // Store references
        connection.client = client;
        connection.process = serverProcess;
        // Set up notification handlers
        this.setupNotificationHandlers(connection);
    }
    /**
     * Connect to an HTTP-based MCP server
     */
    async connectHttpServer(connection) {
        const { config } = connection;
        if (!config.url) {
            throw new Error(`HTTP transport requires 'url' field in server config for ${config.id}`);
        }
        // Create SSE transport
        const transport = new SSEClientTransport(new URL(config.url));
        const client = new Client({
            name: `mcp-proxy-client-${config.id}`,
            version: '1.0.0'
        }, {
            capabilities: {}
        });
        // Connect the client
        await client.connect(transport);
        // Store references
        connection.client = client;
        // Set up notification handlers
        this.setupNotificationHandlers(connection);
    }
    /**
     * Connect to a Streamable HTTP-based MCP server
     */
    async connectStreamableHttpServer(connection) {
        const { config } = connection;
        if (!config.url) {
            throw new Error(`Streamable HTTP transport requires 'url' field in server config for ${config.id}`);
        }
        // Create Streamable HTTP transport
        const transport = new StreamableHTTPClientTransport(new URL(config.url));
        const client = new Client({
            name: `mcp-proxy-client-${config.id}`,
            version: '1.0.0'
        }, {
            capabilities: {}
        });
        // Connect the client
        await client.connect(transport);
        // Store references
        connection.client = client;
        // Set up notification handlers
        this.setupNotificationHandlers(connection);
    }
    /**
     * Load server capabilities (tools, resources, prompts)
     */
    async loadServerCapabilities(connection) {
        const { client } = connection;
        try {
            // Load tools
            try {
                const toolsResponse = await client.request({ method: 'tools/list', params: {} }, ListToolsResultSchema);
                connection.tools = toolsResponse.tools || [];
            }
            catch (error) {
                logger.warn(`Failed to load tools for ${connection.id}, continuing without tools`);
                connection.tools = [];
            }
            // Load resources
            try {
                const resourcesResponse = await client.request({ method: 'resources/list', params: {} }, ListResourcesResultSchema);
                connection.resources = resourcesResponse.resources || [];
            }
            catch (error) {
                logger.warn(`Failed to load resources for ${connection.id}, continuing without resources`);
                connection.resources = [];
            }
            // Load resource templates
            try {
                const resourceTemplatesResponse = await client.request({ method: 'resources/templates/list', params: {} }, ListResourceTemplatesResultSchema);
                connection.resourceTemplates = resourceTemplatesResponse.resourceTemplates || [];
            }
            catch (error) {
                logger.warn(`Failed to load resource templates for ${connection.id}, continuing without resource templates`);
                connection.resourceTemplates = [];
            }
            // Load prompts
            try {
                const promptsResponse = await client.request({ method: 'prompts/list', params: {} }, ListPromptsResultSchema);
                connection.prompts = promptsResponse.prompts || [];
            }
            catch (error) {
                logger.warn(`Failed to load prompts for ${connection.id}, continuing without prompts`);
                connection.prompts = [];
            }
            // Mark capabilities as loaded
            connection.capabilitiesLoaded = true;
            logger.debug(`Loaded capabilities for ${connection.id}:`, {
                tools: connection.tools.length,
                resources: connection.resources.length,
                resourceTemplates: connection.resourceTemplates.length,
                prompts: connection.prompts.length
            });
            this.emit('capabilities-updated', connection.id);
        }
        catch (error) {
            logger.error(`Failed to load capabilities for ${connection.id}:`, error);
            // Even if loading fails, mark as loaded to avoid infinite waiting
            connection.capabilitiesLoaded = true;
            throw error;
        }
    }
    /**
     * Set up notification handlers for a connection
     */
    setupNotificationHandlers(connection) {
        const { client, id } = connection;
        // Handle tool list changes
        client.setNotificationHandler(ToolListChangedNotificationSchema, async () => {
            logger.debug(`Tools changed notification from ${id}`);
            try {
                const response = await client.request({ method: 'tools/list', params: {} }, ListToolsResultSchema);
                connection.tools = response.tools || [];
                this.emit('capabilities-updated', id);
            }
            catch (error) {
                logger.error(`Failed to refresh tools for ${id}:`, error);
            }
        });
        // Handle resource list changes
        client.setNotificationHandler(ResourceListChangedNotificationSchema, async () => {
            logger.debug(`Resources changed notification from ${id}`);
            try {
                const response = await client.request({ method: 'resources/list', params: {} }, ListResourcesResultSchema);
                connection.resources = response.resources || [];
                this.emit('capabilities-updated', id);
            }
            catch (error) {
                logger.error(`Failed to refresh resources for ${id}:`, error);
            }
        });
        // Handle prompt list changes
        client.setNotificationHandler(PromptListChangedNotificationSchema, async () => {
            logger.debug(`Prompts changed notification from ${id}`);
            try {
                const response = await client.request({ method: 'prompts/list', params: {} }, ListPromptsResultSchema);
                connection.prompts = response.prompts || [];
                this.emit('capabilities-updated', id);
            }
            catch (error) {
                logger.error(`Failed to refresh prompts for ${id}:`, error);
            }
        });
    }
    /**
     * Get a backend connection by ID
     */
    getConnection(serverId) {
        return this.connections.get(serverId);
    }
    /**
     * Get all backend connections
     */
    getAllConnections() {
        return Array.from(this.connections.values());
    }
    /**
     * Disconnect from a backend server
     */
    async disconnectServer(serverId) {
        const connection = this.connections.get(serverId);
        if (!connection || !connection.connected) {
            return;
        }
        logger.info(`Disconnecting from server: ${serverId}`);
        try {
            if (connection.client) {
                await connection.client.close();
            }
            // Kill process if it exists
            const process = connection.process;
            if (process && !process.killed) {
                process.kill();
            }
        }
        catch (error) {
            logger.error(`Error disconnecting from ${serverId}:`, error);
        }
        finally {
            connection.connected = false;
            connection.client = null;
            this.emit('server-disconnected', serverId);
        }
    }
    /**
     * Disconnect from all servers
     */
    async disconnectAll() {
        const disconnectPromises = Array.from(this.connections.keys())
            .map(serverId => this.disconnectServer(serverId));
        await Promise.allSettled(disconnectPromises);
    }
}
//# sourceMappingURL=BackendManager.js.map