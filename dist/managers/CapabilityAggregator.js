/**
 * Capability Aggregator - Aggregates capabilities from multiple backend servers
 */
import { EventEmitter } from 'events';
import { logger } from '../utils/logger.js';
export class CapabilityAggregator extends EventEmitter {
    aggregatedCapabilities = {
        tools: [],
        resources: [],
        resourceTemplates: [],
        prompts: [],
        serverCapabilities: {
            tools: { listChanged: true },
            resources: { listChanged: true },
            prompts: { listChanged: true }
        }
    };
    routingMap = new Map();
    /**
     * Update aggregated capabilities from all backend connections
     */
    updateCapabilities(connections) {
        logger.debug('Updating aggregated capabilities');
        // Clear previous state
        this.aggregatedCapabilities.tools = [];
        this.aggregatedCapabilities.resources = [];
        this.aggregatedCapabilities.resourceTemplates = [];
        this.aggregatedCapabilities.prompts = [];
        this.routingMap.clear();
        // Aggregate from all connected servers
        for (const connection of connections) {
            if (!connection.connected)
                continue;
            this.aggregateTools(connection);
            this.aggregateResources(connection);
            this.aggregateResourceTemplates(connection);
            this.aggregatePrompts(connection);
        }
        logger.info('Aggregated capabilities updated:', {
            tools: this.aggregatedCapabilities.tools.length,
            resources: this.aggregatedCapabilities.resources.length,
            prompts: this.aggregatedCapabilities.prompts.length
        });
        this.emit('capabilitiesChanged', this.aggregatedCapabilities);
    }
    /**
     * Aggregate tools from a backend connection
     */
    aggregateTools(connection) {
        for (const tool of connection.tools) {
            const namespacedName = this.getNamespacedName(connection, tool.name);
            // Create namespaced tool
            const namespacedTool = {
                ...tool,
                name: namespacedName
            };
            // Store routing information
            this.routingMap.set(namespacedName, {
                serverId: connection.id,
                originalName: tool.name,
                namespacedName
            });
            this.aggregatedCapabilities.tools.push(namespacedTool);
        }
    }
    /**
     * Aggregate resources from a backend connection
     */
    aggregateResources(connection) {
        for (const resource of connection.resources) {
            const namespacedName = this.getNamespacedName(connection, resource.name);
            // Create namespaced resource
            const namespacedResource = {
                ...resource,
                name: namespacedName
            };
            // Store routing information
            this.routingMap.set(namespacedName, {
                serverId: connection.id,
                originalName: resource.name,
                namespacedName
            });
            this.aggregatedCapabilities.resources.push(namespacedResource);
        }
    }
    /**
     * Aggregate resource templates from a backend connection
     */
    aggregateResourceTemplates(connection) {
        for (const template of connection.resourceTemplates || []) {
            const namespacedName = this.getNamespacedName(connection, template.name);
            // Create namespaced resource template
            const namespacedTemplate = {
                ...template,
                name: namespacedName
            };
            // Store routing information
            this.routingMap.set(namespacedName, {
                serverId: connection.id,
                originalName: template.name,
                namespacedName
            });
            this.aggregatedCapabilities.resourceTemplates.push(namespacedTemplate);
        }
    }
    /**
     * Aggregate prompts from a backend connection
     */
    aggregatePrompts(connection) {
        for (const prompt of connection.prompts) {
            const namespacedName = this.getNamespacedName(connection, prompt.name);
            // Create namespaced prompt
            const namespacedPrompt = {
                ...prompt,
                name: namespacedName
            };
            // Store routing information
            this.routingMap.set(namespacedName, {
                serverId: connection.id,
                originalName: prompt.name,
                namespacedName
            });
            this.aggregatedCapabilities.prompts.push(namespacedPrompt);
        }
    }
    /**
     * Get namespaced name for a capability
     */
    getNamespacedName(connection, originalName) {
        const namespace = connection.config.namespace;
        if (namespace) {
            return `${namespace}.${originalName}`;
        }
        return originalName;
    }
    /**
     * Get routing information for a namespaced name
     */
    getRouteInfo(namespacedName) {
        return this.routingMap.get(namespacedName);
    }
    /**
     * Get all routing information
     */
    getAllRoutes() {
        return new Map(this.routingMap);
    }
    /**
     * Get current aggregated capabilities
     */
    getCapabilities() {
        return { ...this.aggregatedCapabilities };
    }
    /**
     * Check if a tool exists
     */
    hasTool(name) {
        return this.routingMap.has(name);
    }
    /**
     * Check if a resource exists
     */
    hasResource(name) {
        return this.routingMap.has(name);
    }
    /**
     * Check if a prompt exists
     */
    hasPrompt(name) {
        return this.routingMap.has(name);
    }
    /**
     * Get server capabilities that the proxy exposes
     */
    getServerCapabilities() {
        return { ...this.aggregatedCapabilities.serverCapabilities };
    }
}
//# sourceMappingURL=CapabilityAggregator.js.map