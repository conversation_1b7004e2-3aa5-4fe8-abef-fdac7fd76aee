{"version": 3, "file": "CapabilityAggregator.js", "sourceRoot": "", "sources": ["../../src/managers/CapabilityAggregator.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAatC,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAE5C,MAAM,OAAO,oBAAqB,SAAQ,YAAY;IAC5C,sBAAsB,GAA2B;QACvD,KAAK,EAAE,EAAE;QACT,SAAS,EAAE,EAAE;QACb,iBAAiB,EAAE,EAAE;QACrB,OAAO,EAAE,EAAE;QACX,kBAAkB,EAAE;YAClB,KAAK,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;YAC5B,SAAS,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;YAChC,OAAO,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;SAC/B;KACF,CAAC;IAEM,UAAU,GAAG,IAAI,GAAG,EAAqB,CAAC;IAElD;;OAEG;IACH,kBAAkB,CAAC,WAAgC;QACjD,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;QAEjD,uBAAuB;QACvB,IAAI,CAAC,sBAAsB,CAAC,KAAK,GAAG,EAAE,CAAC;QACvC,IAAI,CAAC,sBAAsB,CAAC,SAAS,GAAG,EAAE,CAAC;QAC3C,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,GAAG,EAAE,CAAC;QACnD,IAAI,CAAC,sBAAsB,CAAC,OAAO,GAAG,EAAE,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QAExB,uCAAuC;QACvC,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC,UAAU,CAAC,SAAS;gBAAE,SAAS;YAEpC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAChC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACpC,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC;YAC5C,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;QACpC,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE;YAC9C,KAAK,EAAE,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,MAAM;YAC/C,SAAS,EAAE,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,MAAM;YACvD,OAAO,EAAE,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,MAAM;SACpD,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,UAA6B;QAClD,KAAK,MAAM,IAAI,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;YACpC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAErE,yBAAyB;YACzB,MAAM,cAAc,GAAS;gBAC3B,GAAG,IAAI;gBACP,IAAI,EAAE,cAAc;aACrB,CAAC;YAEF,4BAA4B;YAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,EAAE;gBAClC,QAAQ,EAAE,UAAU,CAAC,EAAE;gBACvB,YAAY,EAAE,IAAI,CAAC,IAAI;gBACvB,cAAc;aACf,CAAC,CAAC;YAEH,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,UAA6B;QACtD,KAAK,MAAM,QAAQ,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;YAC5C,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEzE,6BAA6B;YAC7B,MAAM,kBAAkB,GAAa;gBACnC,GAAG,QAAQ;gBACX,IAAI,EAAE,cAAc;aACrB,CAAC;YAEF,4BAA4B;YAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,EAAE;gBAClC,QAAQ,EAAE,UAAU,CAAC,EAAE;gBACvB,YAAY,EAAE,QAAQ,CAAC,IAAI;gBAC3B,cAAc;aACf,CAAC,CAAC;YAEH,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,UAA6B;QAC9D,KAAK,MAAM,QAAQ,IAAI,UAAU,CAAC,iBAAiB,IAAI,EAAE,EAAE,CAAC;YAC1D,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEzE,sCAAsC;YACtC,MAAM,kBAAkB,GAAqB;gBAC3C,GAAG,QAAQ;gBACX,IAAI,EAAE,cAAc;aACrB,CAAC;YAEF,4BAA4B;YAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,EAAE;gBAClC,QAAQ,EAAE,UAAU,CAAC,EAAE;gBACvB,YAAY,EAAE,QAAQ,CAAC,IAAI;gBAC3B,cAAc;aACf,CAAC,CAAC;YAEH,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,UAA6B;QACpD,KAAK,MAAM,MAAM,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;YACxC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;YAEvE,2BAA2B;YAC3B,MAAM,gBAAgB,GAAW;gBAC/B,GAAG,MAAM;gBACT,IAAI,EAAE,cAAc;aACrB,CAAC;YAEF,4BAA4B;YAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,EAAE;gBAClC,QAAQ,EAAE,UAAU,CAAC,EAAE;gBACvB,YAAY,EAAE,MAAM,CAAC,IAAI;gBACzB,cAAc;aACf,CAAC,CAAC;YAEH,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,UAA6B,EAAE,YAAoB;QAC3E,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC;QAC9C,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,GAAG,SAAS,IAAI,YAAY,EAAE,CAAC;QACxC,CAAC;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,cAAsB;QACjC,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,EAAE,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,IAAY;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,IAAY;QACtB,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,IAAY;QACpB,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,EAAE,GAAG,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,EAAE,CAAC;IAC/D,CAAC;CACF"}