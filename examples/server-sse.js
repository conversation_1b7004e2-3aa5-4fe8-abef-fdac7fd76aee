#!/usr/bin/env node

/**
 * Working SSE MCP Server
 * Based on MCP SDK official examples
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import express from 'express';

const app = express();
const PORT = process.env.PORT || 3004;

// Store transports by session ID
const transports = new Map();

// Create MCP Server
const server = new Server(
  {
    name: 'working-sse-server',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// Define tools
const tools = [
  {
    name: 'hello',
    description: 'Say hello to someone',
    inputSchema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          description: 'Name to greet'
        }
      },
      required: ['name']
    }
  },
  {
    name: 'calculate',
    description: 'Perform basic arithmetic',
    inputSchema: {
      type: 'object',
      properties: {
        operation: {
          type: 'string',
          enum: ['add', 'subtract', 'multiply', 'divide'],
          description: 'Operation to perform'
        },
        a: {
          type: 'number',
          description: 'First number'
        },
        b: {
          type: 'number',
          description: 'Second number'
        }
      },
      required: ['operation', 'a', 'b']
    }
  }
];

// Set up request handlers
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return { tools };
});

server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;

  switch (name) {
    case 'hello':
      return {
        content: [
          {
            type: 'text',
            text: `Hello, ${args.name}! 🎉`
          }
        ]
      };

    case 'calculate':
      let result;
      switch (args.operation) {
        case 'add':
          result = args.a + args.b;
          break;
        case 'subtract':
          result = args.a - args.b;
          break;
        case 'multiply':
          result = args.a * args.b;
          break;
        case 'divide':
          result = args.b !== 0 ? args.a / args.b : 'Error: Division by zero';
          break;
        default:
          result = 'Error: Unknown operation';
      }
      
      return {
        content: [
          {
            type: 'text',
            text: `${args.a} ${args.operation} ${args.b} = ${result}`
          }
        ]
      };

    default:
      return {
        content: [
          {
            type: 'text',
            text: `Error: Unknown tool '${name}'`
          }
        ],
        isError: true
      };
  }
});

// Health check endpoint (no middleware needed)
app.get('/health', (req, res) => {
  res.writeHead(200, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ status: 'ok', timestamp: new Date().toISOString() }));
});

// SSE endpoint for MCP
app.get('/sse', async (req, res) => {
  console.log('New SSE connection request');
  
  try {
    const transport = new SSEServerTransport('/message', res);
    const sessionId = transport.sessionId;
    
    // Store the transport
    transports.set(sessionId, transport);
    
    // Set up cleanup when connection closes
    transport.onclose = () => {
      console.log(`SSE transport closed for session ${sessionId}`);
      transports.delete(sessionId);
    };
    
    // Connect the server to this transport
    await server.connect(transport);
    
    console.log(`SSE MCP server connected with session ${sessionId}`);
  } catch (error) {
    console.error('Error setting up SSE connection:', error);
    if (!res.headersSent) {
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Internal server error' }));
    }
  }
});

// Message endpoint for MCP (no middleware - let SSE transport handle raw body)
app.post('/message', async (req, res) => {
  const sessionId = req.query.sessionId;
  
  if (!sessionId) {
    res.writeHead(400, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Missing sessionId parameter' }));
    return;
  }
  
  const transport = transports.get(sessionId);
  if (!transport) {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Session not found' }));
    return;
  }
  
  try {
    // Handle the message through the transport
    await transport.handlePostMessage(req, res);
  } catch (error) {
    console.error('Error handling message:', error);
    if (!res.headersSent) {
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Internal server error' }));
    }
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`🚀 Working SSE MCP Server running on http://localhost:${PORT}`);
  console.log(`📡 SSE endpoint: http://localhost:${PORT}/sse`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log('');
  console.log('Available tools:');
  tools.forEach(tool => {
    console.log(`  - ${tool.name}: ${tool.description}`);
  });
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down Working SSE MCP Server...');
  process.exit(0);
});
