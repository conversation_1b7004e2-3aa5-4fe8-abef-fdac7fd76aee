#!/usr/bin/env node

/**
 * Streamable HTTP MCP Server
 * A modern MCP server using Streamable HTTP transport
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
// Import from server-everything package which has newer SDK version
// import { StreamableHTTPServerTransport } from '@modelcontextprotocol/server-everything/node_modules/@modelcontextprotocol/sdk/dist/esm/server/streamableHttp.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';

import { randomUUID } from 'crypto';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import express from 'express';
import cors from 'cors';

const app = express();
const PORT = process.env.PORT || 3005;

// Enable CORS
app.use(cors());

// Create server factory function
const createServer = () => {
  const server = new Server(
    {
      name: 'streamable-http-server',
      version: '1.0.0',
    },
    {
      capabilities: {
        tools: {},
      },
    }
  );

  // Set up request handlers
  server.setRequestHandler(ListToolsRequestSchema, async () => ({ tools }));
  server.setRequestHandler(CallToolRequestSchema, async (request) => {
    const { name, arguments: args } = request.params;

    switch (name) {
      case 'weather':
        const temp = Math.floor(Math.random() * 30) + 10;
        const unit = args.unit || 'celsius';
        const unitSymbol = unit === 'fahrenheit' ? '°F' : '°C';

        return {
          content: [
            {
              type: 'text',
              text: `🌤️ Weather in ${args.city}: ${temp}${unitSymbol}, partly cloudy`
            }
          ]
        };

      case 'time':
        const timezone = args.timezone || 'UTC';
        const now = new Date();
        const timeString = now.toLocaleString('en-US', {
          timeZone: timezone === 'UTC' ? 'UTC' : timezone,
          timeZoneName: 'short'
        });

        return {
          content: [
            {
              type: 'text',
              text: `🕐 Current time in ${timezone}: ${timeString}`
            }
          ]
        };

      case 'uuid':
        const uuid = randomUUID();
        return {
          content: [
            {
              type: 'text',
              text: `🆔 Generated UUID: ${uuid}`
            }
          ]
        };

      default:
        return {
          content: [
            {
              type: 'text',
              text: `Error: Unknown tool '${name}'`
            }
          ],
          isError: true
        };
    }
  });

  return server;
};

// Define tools
const tools = [
  {
    name: 'weather',
    description: 'Get weather information for a city',
    inputSchema: {
      type: 'object',
      properties: {
        city: {
          type: 'string',
          description: 'City name'
        },
        unit: {
          type: 'string',
          enum: ['celsius', 'fahrenheit'],
          description: 'Temperature unit',
          default: 'celsius'
        }
      },
      required: ['city']
    }
  },
  {
    name: 'time',
    description: 'Get current time in a timezone',
    inputSchema: {
      type: 'object',
      properties: {
        timezone: {
          type: 'string',
          description: 'Timezone (e.g., UTC, America/New_York)',
          default: 'UTC'
        }
      }
    }
  },
  {
    name: 'uuid',
    description: 'Generate a random UUID',
    inputSchema: {
      type: 'object',
      properties: {}
    }
  }
];

// Request handlers are now defined in the createServer function

// Health check endpoint
app.get('/health', (_req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Stateless Streamable HTTP endpoint for MCP
app.post('/mcp', async (req, res) => {
  console.log(`New Streamable HTTP POST request`);

  try {
    // In stateless mode, create a new instance of transport and server for each request
    // to ensure complete isolation. A single instance would cause request ID collisions
    // when multiple clients connect concurrently.

    const server = createServer();
    const transport = new StreamableHTTPServerTransport({
      sessionIdGenerator: undefined, // No session management for stateless mode
    });

    // Connect the server to the transport
    await server.connect(transport);
    console.log('New stateless Streamable HTTP connection');

    // Handle the request through the transport
    await transport.handleRequest(req, res);

  } catch (error) {
    console.error('Error handling Streamable HTTP request:', error);
    if (!res.headersSent) {
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Internal server error' }));
    }
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`🚀 Streamable HTTP MCP Server running on http://localhost:${PORT}`);
  console.log(`📡 Streamable HTTP endpoint: http://localhost:${PORT}/mcp`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log('');
  console.log('Available tools:');
  tools.forEach(tool => {
    console.log(`  - ${tool.name}: ${tool.description}`);
  });
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down Streamable HTTP MCP Server...');
  process.exit(0);
});
