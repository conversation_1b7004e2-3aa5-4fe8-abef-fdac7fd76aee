{"serverInfo": {"name": "mcp-server-proxy", "version": "1.0.0"}, "servers": [{"id": "everything", "name": "Everything Server", "command": "node", "args": ["node_modules/@modelcontextprotocol/server-everything/dist/index.js"], "transport": "stdio", "namespace": "everything", "enabled": true}, {"id": "simple-sse", "name": "Simple SSE Server", "transport": "http", "url": "http://localhost:3004/sse", "namespace": "sse", "enabled": true}, {"id": "streamable-http", "name": "Streamable HTTP Server", "transport": "streamable", "url": "http://localhost:3005/mcp", "namespace": "stream", "enabled": true}]}