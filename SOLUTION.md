# MCP Server Proxy 解决方案

## 项目概述

本项目实现了一个完整的 MCP (Model Context Protocol) Server Proxy，作为中间层聚合多个 MCP 服务器，使 LLM 客户端能够通过单一连接访问多个服务器的功能。

## 核心功能

### 1. 协议兼容性
- **完全兼容 MCP 协议规范**: 支持所有标准 MCP 消息类型
- **JSON-RPC 2.0 基础**: 基于标准 JSON-RPC 2.0 协议实现
- **生命周期管理**: 完整的 initialize/initialized 握手流程
- **能力协商**: 动态聚合和暴露后端服务器能力

### 2. 🚀 多传输协议支持
#### 代理对外服务
- **stdio 传输**: 标准输入输出流，最低延迟，适用于命令行工具
- **HTTP (SSE) 传输**: Server-Sent Events，Web 友好，支持 CORS
- **Streamable HTTP 传输**: 现代双向流式 HTTP，支持会话管理和无状态模式

#### 后端服务器连接
- **混合协议支持**: 同时连接 stdio、HTTP、Streamable HTTP 后端服务器
- **统一抽象层**: 传输层抽象，易于扩展新协议
- **配置驱动**: 通过配置文件灵活选择传输协议

### 3. 🔗 多服务器聚合
- **动态连接管理**: 支持多个后端 MCP 服务器的并发连接
- **实时能力聚合**: 将多个服务器的 tools、resources、prompts 动态统一暴露
- **命名空间支持**: 通过命名空间前缀避免不同服务器间的命名冲突
- **故障隔离**: 单个服务器故障不影响其他服务器的正常工作
- **实际验证**: 成功聚合 16 个工具（Everything + SSE + Streamable HTTP）

### 4. 🎯 智能路由
- **自动请求路由**: 根据 tool/resource/prompt 名称智能路由到对应服务器
- **命名空间转换**: 自动处理客户端和服务器间的命名空间映射
- **并发处理**: 支持多个并发请求的独立处理
- **错误隔离**: 单个请求失败不影响其他请求
- **Schema 修复**: 修复了关键的 ZodError 和请求格式问题

### 5. 📡 实时通知转发
- **事件监听**: 监听所有后端服务器的通知事件
- **智能转发**: 将后端服务器的通知实时转发给客户端
- **连接状态管理**: 优雅处理客户端连接状态，避免 "Not connected" 错误
- **通知防抖**: 避免短时间内的重复通知

## 技术架构

### 核心模块设计

```
┌─────────────────────────────────────────────────────────────┐
│                    LLM 客户端                                │
│              (Cursor, Claude Desktop)                       │
└─────────────────────┬───────────────────────────────────────┘
                      │ JSON-RPC 2.0
┌─────────────────────▼───────────────────────────────────────┐
│                MCP Server Proxy                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐│
│  │   Proxy     │ │ Capability  │ │    Request Router       ││
│  │   Server    │ │ Aggregator  │ │                         ││
│  └─────────────┘ └─────────────┘ └─────────────────────────┘│
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐│
│  │  Backend    │ │ Notification│ │    Connection Manager   ││
│  │  Manager    │ │  Manager    │ │                         ││
│  └─────────────┘ └─────────────┘ └─────────────────────────┘│
└─────────────────────┬───────────────────────────────────────┘
                      │ stdio/HTTP
┌─────────────────────▼───────────────────────────────────────┐
│                后端 MCP 服务器                               │
│    ┌─────────────┐ ┌─────────────┐ ┌─────────────┐         │
│    │ Filesystem  │ │  Database   │ │    API      │         │
│    │   Server    │ │   Server    │ │   Server    │         │
│    └─────────────┘ └─────────────┘ └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 关键组件

#### 1. MCPServerProxy (主代理服务器)
- **职责**: MCP 协议的主要入口点，协调各个管理器
- **功能**: 
  - 处理客户端连接和协议握手
  - 协调各管理器之间的交互
  - 管理服务器生命周期

#### 2. BackendManager (后端管理器)
- **职责**: 管理与多个 MCP 服务器的连接
- **功能**:
  - 维护 MCP 客户端连接池
  - 处理服务器启动/停止/重连
  - 监控连接健康状态

#### 3. CapabilityAggregator (能力聚合器)
- **职责**: 聚合所有后端服务器的能力
- **功能**:
  - 合并 tools/resources/prompts 列表
  - 处理命名冲突和命名空间
  - 维护能力到服务器的映射关系

#### 4. RequestRouter (请求路由器)
- **职责**: 将客户端请求路由到正确的后端服务器
- **功能**:
  - 解析请求中的目标标识
  - 执行命名空间转换
  - 处理路由失败和错误恢复

#### 5. NotificationManager (通知管理器)
- **职责**: 处理来自后端服务器的通知
- **功能**:
  - 监听后端服务器通知
  - 实现通知防抖和聚合
  - 转发通知给客户端

## MCP 协议理解

### 协议基础
MCP 基于 JSON-RPC 2.0 协议，采用客户端-服务器架构：

1. **传输层**: 支持 stdio 和 HTTP 两种传输方式
2. **消息格式**: 标准 JSON-RPC 2.0 消息格式
3. **生命周期**: initialize → initialized → 正常通信 → 关闭

### 核心概念

#### Tools (工具)
- **定义**: 可执行的函数，如文件操作、API 调用
- **特点**: 有副作用，可以改变系统状态
- **使用**: `tools/list` 获取列表，`tools/call` 执行工具

#### Resources (资源)
- **定义**: 上下文数据源，如文件内容、数据库记录
- **特点**: 只读，提供信息但不改变状态
- **使用**: `resources/list` 获取列表，`resources/read` 读取内容

#### Prompts (提示)
- **定义**: 可重用的提示模板
- **特点**: 结构化的对话模板
- **使用**: `prompts/list` 获取列表，`prompts/get` 获取内容

### 通信流程

```
Client                    Proxy                     Backend Server
  │                        │                            │
  ├─ initialize ──────────▶│                            │
  │                        ├─ initialize ─────────────▶│
  │                        │◀─ initialized ─────────────┤
  │◀─ initialized ─────────┤                            │
  │                        │                            │
  ├─ tools/list ──────────▶│                            │
  │                        ├─ tools/list ──────────────▶│
  │                        │◀─ tools response ──────────┤
  │◀─ aggregated response ─┤                            │
  │                        │                            │
  ├─ tools/call ──────────▶│                            │
  │                        ├─ tools/call ──────────────▶│
  │                        │◀─ call result ─────────────┤
  │◀─ result ──────────────┤                            │
```

## 代理设计思路

### 1. 透明代理模式
代理对客户端完全透明，客户端无需知道后端有多个服务器：
- 客户端看到的是一个统一的 MCP 服务器
- 所有复杂性都在代理内部处理
- 保持标准 MCP 协议兼容性

### 2. 命名空间机制
通过命名空间避免不同服务器间的冲突：
```json
{
  "name": "fs.list_files",  // 带命名空间的名称
  "original": "list_files", // 原始名称
  "server": "filesystem"    // 来源服务器
}
```

### 3. 智能路由算法
1. 解析请求中的 tool/resource/prompt 名称
2. 查找路由映射表确定目标服务器
3. 转换命名空间名称为原始名称
4. 转发请求到目标服务器
5. 处理响应并返回给客户端

### 4. 故障处理策略
- **连接失败**: 记录错误，继续其他服务器
- **请求超时**: 返回标准错误响应
- **服务器崩溃**: 自动重连机制
- **部分失败**: 返回可用服务器的结果

## 实现特点

### 1. 模块化设计
- 每个组件职责单一，易于测试和维护
- 使用 EventEmitter 实现组件间解耦
- 支持插件式扩展

### 2. 类型安全
- 使用 TypeScript 实现完整类型定义
- 严格的接口约束确保代码质量
- 编译时错误检查

### 3. 配置驱动
- JSON 配置文件定义后端服务器
- 支持环境变量覆盖
- 热重载配置更新

### 4. 可观测性
- 结构化日志记录
- 详细的错误信息
- 性能指标收集

## 配置示例

```json
{
  "serverInfo": {
    "name": "mcp-server-proxy",
    "version": "1.0.0"
  },
  "servers": [
    {
      "id": "filesystem",
      "name": "Filesystem Server",
      "command": "npx",
      "args": ["@modelcontextprotocol/server-filesystem", "/tmp"],
      "transport": "stdio",
      "namespace": "fs",
      "enabled": true
    },
    {
      "id": "database",
      "name": "Database Server", 
      "command": "python",
      "args": ["db_server.py"],
      "transport": "stdio",
      "namespace": "db",
      "enabled": true
    }
  ]
}
```

## 使用方法

### 1. 安装依赖
```bash
npm install
```

### 2. 构建项目
```bash
npm run build
```

### 3. 启动代理
```bash
node dist/index.js config.json
```

### 4. 使用 MCP Inspector 测试
```bash
npx @modelcontextprotocol/inspector node dist/index.js config.json
```

## 测试验证

### 1. 基本功能测试
- ✅ 代理服务器成功启动
- ✅ 配置文件正确加载
- ✅ MCP Inspector 连接成功
- ✅ 协议握手正常完成

### 2. 多传输协议测试
- ✅ stdio 传输: Everything Server (11 工具) + Memory Server (9 工具)
- ✅ HTTP (SSE) 传输: Working SSE Server (2 工具)
- ✅ Streamable HTTP 传输: Weather/Time/UUID 工具 (3 工具)
- ✅ 工具调用成功: `stream.weather` 返回 "🌤️ Weather in Beijing: 24°C, partly cloudy"

### 3. 兼容性测试
- ✅ 支持标准 MCP 协议消息
- ✅ JSON-RPC 2.0 格式正确
- ✅ 错误处理符合规范
- ✅ MCP SDK v1.17.5 兼容

### 4. 集成测试
- ✅ 与 everything server 集成测试
- ✅ 多服务器聚合测试 (最多 25 个工具)
- ✅ 命名空间功能测试 (everything.*, mem.*, sse.*, stream.*)

## 扩展性考虑

### 1. 传输层扩展
- 当前支持 stdio 传输
- 可扩展支持 HTTP/WebSocket
- 支持自定义传输协议

### 2. 认证授权
- 可添加认证中间件
- 支持基于角色的访问控制
- 集成外部认证系统

### 3. 缓存优化
- 可添加响应缓存层
- 支持智能缓存策略
- 减少后端服务器负载

### 4. 监控告警
- 集成监控系统
- 健康检查端点
- 性能指标暴露

## 总结

本 MCP Server Proxy 实现了一个功能完整、架构清晰的代理服务器，具有以下优势：

1. **完全兼容**: 严格遵循 MCP 协议规范
2. **高可用性**: 故障隔离和自动恢复机制
3. **易于扩展**: 模块化设计支持功能扩展
4. **生产就绪**: 完善的错误处理和日志记录
5. **开发友好**: 详细的文档和示例代码

该解决方案成功解决了多 MCP 服务器聚合的核心问题，为 LLM 应用提供了统一的上下文访问接口。
