/**
 * MCP Server Proxy - Main proxy server implementation
 */

import { EventEmitter } from 'events';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  ListToolsRequestSchema,
  CallToolRequestSchema,
  ListResourcesRequestSchema,
  ListResourceTemplatesRequestSchema,
  ReadResourceRequestSchema,
  ListPromptsRequestSchema,
  GetPromptRequestSchema
} from '@modelcontextprotocol/sdk/types.js';

import { BackendManager } from '../managers/BackendManager.js';
import { CapabilityAggregator } from '../managers/CapabilityAggregator.js';
import { RequestRouter } from '../managers/RequestRouter.js';
import { NotificationManager } from '../managers/NotificationManager.js';
import { ProxyConfig } from '../types/index.js';
import { logger } from '../utils/logger.js';

/**
 * Proxy server state
 */
interface ProxyState {
  initialized: boolean;
  clientCapabilities?: any;
}

/**
 * Main MCP Server Proxy class
 */
export class MCPServerProxy extends EventEmitter {
  private server: Server;
  private backendManager: BackendManager;
  private capabilityAggregator: CapabilityAggregator;
  private requestRouter: RequestRouter;
  private notificationManager: NotificationManager;
  private config: ProxyConfig;
  private state: ProxyState;
  private notificationDebounceTimer: NodeJS.Timeout | null = null;
  private pendingNotifications = new Set<string>();

  constructor(config: ProxyConfig) {
    super();

    this.config = config;
    this.state = {
      initialized: false
    };

    // Initialize MCP server with static capabilities
    // Note: MCP SDK requires capabilities to be declared at initialization
    this.server = new Server(
      {
        name: config.serverInfo.name,
        version: config.serverInfo.version
      },
      {
        capabilities: {
          tools: {
            listChanged: true
          },
          resources: {
            subscribe: true,
            listChanged: true
          },
          prompts: {
            listChanged: true
          }
        }
      }
    );

    // Initialize managers
    this.backendManager = new BackendManager();
    this.capabilityAggregator = new CapabilityAggregator();
    this.requestRouter = new RequestRouter(this.backendManager, this.capabilityAggregator);
    this.notificationManager = new NotificationManager(this.backendManager);

    this.setupEventHandlers();
    this.setupServerHandlers();
  }

  /**
   * Set up event handlers between managers
   */
  private setupEventHandlers(): void {
    // Backend manager events
    this.backendManager.on('server-connected', (serverId: string) => {
      logger.info(`Backend server connected: ${serverId}`);
      this.updateAggregatedCapabilities();
    });

    this.backendManager.on('server-disconnected', (serverId: string) => {
      logger.info(`Backend server disconnected: ${serverId}`);
      this.updateAggregatedCapabilities();
    });

    this.backendManager.on('capabilities-updated', (serverId: string) => {
      logger.debug(`Capabilities updated for server: ${serverId}`);
      this.updateAggregatedCapabilities();
    });

    // Capability aggregator events
    this.capabilityAggregator.on('capabilitiesChanged', () => {
      logger.debug('Capabilities changed, notifying clients');
      // Send notifications to clients about capability changes
      this.sendCapabilityChangeNotifications();
    });

    // Notification manager events
    this.notificationManager.on('notification', (notification: any) => {
      this.forwardNotification(notification);
    });

    // Handle backend capabilities changes
    this.notificationManager.on('backend-capabilities-changed', (serverId: string, changeType: string) => {
      logger.debug(`Backend capabilities changed for ${serverId}: ${changeType}`);
      this.updateAggregatedCapabilities();
    });
  }

  /**
   * Set up MCP server handlers using the low-level API
   */
  private setupServerHandlers(): void {
    // Set up all required MCP request handlers
    this.setupRequestHandlers();
  }

  /**
   * Set up all MCP request handlers
   */
  private setupRequestHandlers(): void {
    // Tools handlers
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      const capabilities = this.capabilityAggregator.getCapabilities();
      return { tools: capabilities.tools };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      // Convert to JSONRPCRequest format for router
      const jsonRpcRequest = {
        jsonrpc: '2.0' as const,
        id: Math.random().toString(36),
        method: 'tools/call',
        params: request.params
      };
      const response = await this.requestRouter.routeRequest(jsonRpcRequest);
      return response.result;
    });

    // Resources handlers
    this.server.setRequestHandler(ListResourcesRequestSchema, async () => {
      const capabilities = this.capabilityAggregator.getCapabilities();
      return { resources: capabilities.resources };
    });

    this.server.setRequestHandler(ListResourceTemplatesRequestSchema, async () => {
      const capabilities = this.capabilityAggregator.getCapabilities();
      return { resourceTemplates: capabilities.resourceTemplates || [] };
    });

    this.server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
      // Convert to JSONRPCRequest format for router
      const jsonRpcRequest = {
        jsonrpc: '2.0' as const,
        id: Math.random().toString(36),
        method: 'resources/read',
        params: request.params
      };
      const response = await this.requestRouter.routeRequest(jsonRpcRequest);
      return response.result;
    });

    // Prompts handlers
    this.server.setRequestHandler(ListPromptsRequestSchema, async () => {
      const capabilities = this.capabilityAggregator.getCapabilities();
      return { prompts: capabilities.prompts };
    });

    this.server.setRequestHandler(GetPromptRequestSchema, async (request) => {
      // Convert to JSONRPCRequest format for router
      const jsonRpcRequest = {
        jsonrpc: '2.0' as const,
        id: Math.random().toString(36),
        method: 'prompts/get',
        params: request.params
      };
      const response = await this.requestRouter.routeRequest(jsonRpcRequest);
      return response.result;
    });

    logger.debug('All MCP request handlers set up');
  }

  /**
   * Get dynamic capabilities based on current backend connections
   */
  private getDynamicCapabilities(): any {
    const aggregatedCapabilities = this.capabilityAggregator.getCapabilities();

    return {
      tools: aggregatedCapabilities.tools.length > 0 ? {} : undefined,
      resources: aggregatedCapabilities.resources.length > 0 ? {} : undefined,
      prompts: aggregatedCapabilities.prompts.length > 0 ? {} : undefined
    };
  }

  /**
   * Update aggregated capabilities from all backend connections
   */
  private updateAggregatedCapabilities(): void {
    const connections = this.backendManager.getAllConnections();
    this.capabilityAggregator.updateCapabilities(connections);

    // Update server capabilities after aggregation
    this.updateServerCapabilities();
  }

  /**
   * Update server capabilities registration
   */
  private updateServerCapabilities(): void {
    try {
      const capabilities = this.getDynamicCapabilities();

      // Re-register capabilities with the server
      // Note: This is a workaround since MCP SDK doesn't support dynamic capability updates
      logger.debug('Server capabilities updated:', capabilities);
    } catch (error) {
      logger.error('Error updating server capabilities:', error);
    }
  }

  /**
   * Set up dynamic handlers based on aggregated capabilities
   */
  private setupDynamicHandlers(): void {
    // For now, we'll use a simpler approach with the low-level API
    // The actual request routing will be handled by the RequestRouter
    logger.debug('Dynamic handlers setup - using low-level API routing');
  }



  /**
   * Send capability change notifications with debouncing
   */
  private async sendCapabilityChangeNotifications(): Promise<void> {
    // Clear existing timer
    if (this.notificationDebounceTimer) {
      clearTimeout(this.notificationDebounceTimer);
    }

    // Set up debounced notification sending
    this.notificationDebounceTimer = setTimeout(async () => {
      try {
        await this.sendDebouncedNotifications();
      } catch (error) {
        logger.error('Error sending debounced notifications:', error);
      }
    }, 100); // 100ms debounce
  }

  /**
   * Send the actual notifications after debouncing
   */
  private async sendDebouncedNotifications(): Promise<void> {
    if (!this.state.initialized) {
      logger.debug('Proxy not initialized, skipping capability notifications');
      return;
    }

    try {
      // Update dynamic handlers when capabilities change
      this.setupDynamicHandlers();

      // Get current capabilities to determine what changed
      const capabilities = this.capabilityAggregator.getCapabilities();

      // Send notifications for each capability type that has items
      const notifications: string[] = [];

      if (capabilities.tools.length > 0) {
        notifications.push('notifications/tools/list_changed');
      }

      if (capabilities.resources.length > 0) {
        notifications.push('notifications/resources/list_changed');
      }

      if (capabilities.prompts.length > 0) {
        notifications.push('notifications/prompts/list_changed');
      }

      // Send notifications to client
      for (const method of notifications) {
        if (!this.pendingNotifications.has(method)) {
          this.pendingNotifications.add(method);

          try {
            await this.server.notification({
              method,
              params: {}
            });
            logger.debug(`Sent capability notification: ${method}`);
          } catch (error) {
            if (error instanceof Error && error.message.includes('Not connected')) {
              logger.debug('No client connected for capability notification');
            } else {
              logger.error(`Error sending capability notification ${method}:`, error);
            }
          }

          // Remove from pending after a short delay
          setTimeout(() => {
            this.pendingNotifications.delete(method);
          }, 1000);
        }
      }

      logger.debug('Capability change notifications processing completed');
    } catch (error) {
      logger.error('Error in sendDebouncedNotifications:', error);
    }
  }

  /**
   * Forward notification to clients
   */
  private async forwardNotification(notification: any): Promise<void> {
    try {
      // Extract method and params from the notification
      const { method, params } = notification;

      if (!method) {
        logger.warn('Notification missing method, skipping:', notification);
        return;
      }

      // Only send notifications if the proxy is fully initialized
      if (!this.state.initialized) {
        logger.debug('Proxy not fully initialized, skipping notification:', method);
        return;
      }

      // Use the server's notification method with correct format
      await this.server.notification({ method, params });
      logger.debug('Notification forwarded to clients:', method);
    } catch (error) {
      if (error instanceof Error && error.message.includes('Not connected')) {
        logger.debug('No client connected for notification');
      } else {
        logger.error('Error forwarding notification:', error);
      }
    }
  }

  /**
   * Start the proxy server
   */
  async start(): Promise<void> {
    try {
      logger.info('Starting MCP Server Proxy...');

      // First, connect to all backend servers and wait for them to be ready
      await this.connectAllBackendServers();

      // Wait a bit more to ensure all capabilities are loaded
      await this.waitForCapabilitiesReady();

      // Now set up transport and connect to accept client connections
      const transport = new StdioServerTransport();
      await this.server.connect(transport);

      this.state.initialized = true;

      logger.info('MCP Server Proxy started successfully');
      logger.info('Ready to accept client connections');

      this.emit('started');
    } catch (error) {
      logger.error('Failed to start MCP Server Proxy:', error);
      throw error;
    }
  }

  /**
   * Connect to all backend servers and wait for them to be ready
   */
  private async connectAllBackendServers(): Promise<void> {
    const enabledServers = this.config.servers.filter(s => s.enabled);

    if (enabledServers.length === 0) {
      logger.warn('No enabled backend servers found');
      return;
    }

    logger.info(`Connecting to ${enabledServers.length} backend servers...`);

    // Connect to all servers in parallel
    const connectionPromises = enabledServers.map(async (serverConfig) => {
      try {
        await this.backendManager.addServer(serverConfig);
        logger.info(`✓ Connected to ${serverConfig.id}`);
      } catch (error) {
        logger.error(`✗ Failed to connect to ${serverConfig.id}:`, error);
        throw error;
      }
    });

    await Promise.all(connectionPromises);
    logger.info('All backend servers connected');
  }

  /**
   * Wait for all capabilities to be loaded and aggregated
   */
  private async waitForCapabilitiesReady(): Promise<void> {
    const maxWaitTime = 5000; // 5 seconds max wait
    const checkInterval = 100; // Check every 100ms
    let waitTime = 0;

    logger.info('Waiting for capabilities to be ready...');

    while (waitTime < maxWaitTime) {
      const connections = this.backendManager.getAllConnections();
      const connectedServers = connections.filter(conn => conn.connected);

      // Check if all connected servers have loaded their capabilities
      const allCapabilitiesLoaded = connectedServers.every(conn => conn.capabilitiesLoaded);

      if (connectedServers.length > 0 && allCapabilitiesLoaded) {
        // Force final capability aggregation
        this.updateAggregatedCapabilities();

        const capabilities = this.capabilityAggregator.getCapabilities();
        logger.info('Capabilities ready:', {
          tools: capabilities.tools.length,
          resources: capabilities.resources.length,
          prompts: capabilities.prompts.length
        });
        return;
      }

      await new Promise(resolve => setTimeout(resolve, checkInterval));
      waitTime += checkInterval;
    }

    // If we reach here, some capabilities might not be loaded, but continue anyway
    logger.warn('Timeout waiting for all capabilities, continuing with available capabilities');
    this.updateAggregatedCapabilities();

    const capabilities = this.capabilityAggregator.getCapabilities();
    logger.info('Available capabilities:', {
      tools: capabilities.tools.length,
      resources: capabilities.resources.length,
      prompts: capabilities.prompts.length
    });
  }

  /**
   * Stop the proxy server
   */
  async stop(): Promise<void> {
    try {
      logger.info('Stopping MCP Server Proxy...');

      // Clear any pending notification timers
      if (this.notificationDebounceTimer) {
        clearTimeout(this.notificationDebounceTimer);
        this.notificationDebounceTimer = null;
      }

      // Clear pending notifications
      this.pendingNotifications.clear();

      // Disconnect from backend servers
      await this.backendManager.disconnectAll();

      // Close server
      if (this.server) {
        await this.server.close();
      }

      this.state.initialized = false;
      logger.info('MCP Server Proxy stopped');

      this.emit('stopped');
    } catch (error) {
      logger.error('Error stopping MCP Server Proxy:', error);
      throw error;
    }
  }

  /**
   * Get proxy status
   */
  getStatus(): any {
    return {
      initialized: this.state.initialized,
      backendServers: this.backendManager.getAllConnections().map(conn => ({
        id: conn.id,
        connected: conn.connected,
        name: conn.config.name
      })),
      capabilities: this.capabilityAggregator.getCapabilities()
    };
  }
}