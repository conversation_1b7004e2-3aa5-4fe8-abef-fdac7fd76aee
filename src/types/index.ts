/**
 * Type definitions for MCP Server Proxy
 */

import {
  Tool,
  Resource,
  ResourceTemplate,
  Prompt,
  ServerCapabilities,
  ClientCapabilities,
  JSONRPCMessage,
  JSONRPCRequest,
  JSONRPCResponse,
  JSONRPCNotification
} from '@modelcontextprotocol/sdk/types.js';

/**
 * Configuration for a backend MCP server
 */
export interface BackendServerConfig {
  /** Unique identifier for this server */
  id: string;
  /** Human-readable name */
  name: string;
  /** Command to start the server */
  command: string;
  /** Arguments for the command */
  args?: string[];
  /** Environment variables */
  env?: Record<string, string>;
  /** Transport type */
  transport: 'stdio' | 'http' | 'streamable';
  /** HTTP URL if using HTTP transport */
  url?: string;
  /** Namespace prefix for tools/resources/prompts */
  namespace?: string;
  /** Whether this server is enabled */
  enabled: boolean;
}

/**
 * Proxy configuration
 */
export interface ProxyConfig {
  /** Port to listen on (for HTTP transport) */
  port?: number;
  /** Backend servers to connect to */
  servers: BackendServerConfig[];
  /** Proxy server info */
  serverInfo: {
    name: string;
    version: string;
  };
}

/**
 * Backend server connection state
 */
export interface BackendConnection {
  id: string;
  config: BackendServerConfig;
  client: any; // MCP Client instance
  capabilities?: ServerCapabilities;
  connected: boolean;
  capabilitiesLoaded: boolean;
  lastError?: Error;
  tools: Tool[];
  resources: Resource[];
  resourceTemplates: ResourceTemplate[];
  prompts: Prompt[];
}

/**
 * Aggregated capabilities from all backend servers
 */
export interface AggregatedCapabilities {
  tools: Tool[];
  resources: Resource[];
  resourceTemplates: ResourceTemplate[];
  prompts: Prompt[];
  serverCapabilities: ServerCapabilities;
}

/**
 * Request routing information
 */
export interface RouteInfo {
  serverId: string;
  originalName: string;
  namespacedName: string;
}

/**
 * Proxy server events
 */
export interface ProxyEvents {
  'server-connected': (serverId: string) => void;
  'server-disconnected': (serverId: string, error?: Error) => void;
  'capabilities-updated': (serverId: string) => void;
  'notification-received': (serverId: string, notification: JSONRPCNotification) => void;
}

/**
 * Extended JSON-RPC message types for internal routing
 */
export interface RoutedJSONRPCRequest extends JSONRPCRequest {
  _route?: RouteInfo;
}

export interface RoutedJSONRPCResponse extends JSONRPCResponse {
  _route?: RouteInfo;
}

/**
 * Proxy server state
 */
export interface ProxyState {
  initialized: boolean;
  clientCapabilities?: ClientCapabilities;
  backendConnections: Map<string, BackendConnection>;
  aggregatedCapabilities: AggregatedCapabilities;
}
