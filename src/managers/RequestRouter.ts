/**
 * Request Router - Routes requests to appropriate backend servers
 */

import {
  JSONRPCRequest,
  JSONRPCResponse,
  JSONRPCError,
  CallToolResultSchema,
  ReadResourceResultSchema,
  GetPromptResultSchema
} from '@modelcontextprotocol/sdk/types.js';
import { z } from 'zod';
import { 
  BackendConnection, 
  RouteInfo,
  RoutedJSONRPCRequest 
} from '../types/index.js';
import { CapabilityAggregator } from './CapabilityAggregator.js';
import { BackendManager } from './BackendManager.js';
import { logger } from '../utils/logger.js';

export class RequestRouter {
  constructor(
    private backendManager: BackendManager,
    private capabilityAggregator: CapabilityAggregator
  ) {}

  /**
   * Route a request to the appropriate backend server
   */
  async routeRequest(request: JSONRPCRequest): Promise<JSONRPCResponse> {
    logger.debug(`Routing request: ${request.method}`, { id: request.id });

    try {
      // Handle different request types
      switch (request.method) {
        case 'tools/list':
          return this.handleToolsList(request);
        
        case 'tools/call':
          return await this.handleToolsCall(request);
        
        case 'resources/list':
          return this.handleResourcesList(request);
        
        case 'resources/read':
          return await this.handleResourcesRead(request);
        
        case 'prompts/list':
          return this.handlePromptsList(request);
        
        case 'prompts/get':
          return await this.handlePromptsGet(request);
        
        default:
          // For unknown methods, try to route to first available server
          return await this.handleGenericRequest(request);
      }
    } catch (error) {
      logger.error(`Error routing request ${request.method}:`, error);
      
      throw new Error(`Internal error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Handle tools/list request
   */
  private handleToolsList(request: JSONRPCRequest): JSONRPCResponse {
    const capabilities = this.capabilityAggregator.getCapabilities();
    
    return {
      jsonrpc: '2.0',
      id: request.id,
      result: {
        tools: capabilities.tools
      }
    };
  }

  /**
   * Handle tools/call request
   */
  private async handleToolsCall(request: JSONRPCRequest): Promise<JSONRPCResponse> {
    const params = request.params as any;
    const toolName = params?.name;
    
    if (!toolName) {
      return this.createErrorResponse(request.id, -32602, 'Invalid params: missing tool name');
    }

    // Get routing information
    const routeInfo = this.capabilityAggregator.getRouteInfo(toolName);
    if (!routeInfo) {
      return this.createErrorResponse(request.id, -32601, `Tool not found: ${toolName}`);
    }

    // Get backend connection
    const connection = this.backendManager.getConnection(routeInfo.serverId);
    if (!connection || !connection.connected) {
      return this.createErrorResponse(request.id, -32603, `Backend server not available: ${routeInfo.serverId}`);
    }

    // Create request with original tool name
    const backendRequest: JSONRPCRequest = {
      ...request,
      params: {
        ...params,
        name: routeInfo.originalName
      }
    };

    // Forward to backend server
    try {
      const response = await connection.client.request(
        {
          method: 'tools/call',
          params: {
            ...params,
            name: routeInfo.originalName
          }
        },
        CallToolResultSchema
      );
      return {
        jsonrpc: '2.0',
        id: request.id,
        result: response
      };
    } catch (error) {
      logger.error(`Tool call failed for ${toolName}:`, error);
      return this.createErrorResponse(request.id, -32603, `Tool execution failed: ${error}`);
    }
  }

  /**
   * Handle resources/list request
   */
  private handleResourcesList(request: JSONRPCRequest): JSONRPCResponse {
    const capabilities = this.capabilityAggregator.getCapabilities();
    
    return {
      jsonrpc: '2.0',
      id: request.id,
      result: {
        resources: capabilities.resources
      }
    };
  }

  /**
   * Handle resources/read request
   */
  private async handleResourcesRead(request: JSONRPCRequest): Promise<JSONRPCResponse> {
    const params = request.params as any;
    const resourceUri = params?.uri;
    
    if (!resourceUri) {
      return this.createErrorResponse(request.id, -32602, 'Invalid params: missing resource URI');
    }

    // Extract resource name from URI (assuming format like "resource://name")
    const resourceName = this.extractResourceName(resourceUri);
    if (!resourceName) {
      return this.createErrorResponse(request.id, -32602, 'Invalid resource URI format');
    }

    // Get routing information
    const routeInfo = this.capabilityAggregator.getRouteInfo(resourceName);
    if (!routeInfo) {
      return this.createErrorResponse(request.id, -32601, `Resource not found: ${resourceName}`);
    }

    // Get backend connection
    const connection = this.backendManager.getConnection(routeInfo.serverId);
    if (!connection || !connection.connected) {
      return this.createErrorResponse(request.id, -32603, `Backend server not available: ${routeInfo.serverId}`);
    }

    // Create request with original resource name
    const originalUri = resourceUri.replace(resourceName, routeInfo.originalName);
    const backendRequest: JSONRPCRequest = {
      ...request,
      params: {
        ...params,
        uri: originalUri
      }
    };

    // Forward to backend server
    try {
      const response = await connection.client.request(
        {
          method: 'resources/read',
          params: {
            ...params,
            uri: originalUri
          }
        },
        ReadResourceResultSchema
      );
      return {
        jsonrpc: '2.0',
        id: request.id,
        result: response
      };
    } catch (error) {
      logger.error(`Resource read failed for ${resourceName}:`, error);
      return this.createErrorResponse(request.id, -32603, `Resource read failed: ${error}`);
    }
  }

  /**
   * Handle prompts/list request
   */
  private handlePromptsList(request: JSONRPCRequest): JSONRPCResponse {
    const capabilities = this.capabilityAggregator.getCapabilities();
    
    return {
      jsonrpc: '2.0',
      id: request.id,
      result: {
        prompts: capabilities.prompts
      }
    };
  }

  /**
   * Handle prompts/get request
   */
  private async handlePromptsGet(request: JSONRPCRequest): Promise<JSONRPCResponse> {
    const params = request.params as any;
    const promptName = params?.name;
    
    if (!promptName) {
      return this.createErrorResponse(request.id, -32602, 'Invalid params: missing prompt name');
    }

    // Get routing information
    const routeInfo = this.capabilityAggregator.getRouteInfo(promptName);
    if (!routeInfo) {
      return this.createErrorResponse(request.id, -32601, `Prompt not found: ${promptName}`);
    }

    // Get backend connection
    const connection = this.backendManager.getConnection(routeInfo.serverId);
    if (!connection || !connection.connected) {
      return this.createErrorResponse(request.id, -32603, `Backend server not available: ${routeInfo.serverId}`);
    }

    // Create request with original prompt name
    const backendRequest: JSONRPCRequest = {
      ...request,
      params: {
        ...params,
        name: routeInfo.originalName
      }
    };

    // Forward to backend server
    try {
      const response = await connection.client.request(
        {
          method: 'prompts/get',
          params: {
            ...params,
            name: routeInfo.originalName
          }
        },
        GetPromptResultSchema
      );
      return {
        jsonrpc: '2.0',
        id: request.id,
        result: response
      };
    } catch (error) {
      logger.error(`Prompt get failed for ${promptName}:`, error);
      return this.createErrorResponse(request.id, -32603, `Prompt get failed: ${error}`);
    }
  }

  /**
   * Handle generic requests by forwarding to first available server
   */
  private async handleGenericRequest(request: JSONRPCRequest): Promise<JSONRPCResponse> {
    const connections = this.backendManager.getAllConnections();
    const connectedServers = connections.filter(conn => conn.connected);
    
    if (connectedServers.length === 0) {
      return this.createErrorResponse(request.id, -32603, 'No backend servers available');
    }

    // Try first available server
    const connection = connectedServers[0];
    
    try {
      const response = await connection.client.request(
        {
          method: request.method,
          params: request.params
        },
        z.any() // Use any schema for generic requests
      );
      return {
        jsonrpc: '2.0',
        id: request.id,
        result: response
      };
    } catch (error) {
      logger.error(`Generic request failed:`, error);
      return this.createErrorResponse(request.id, -32601, `Method not found: ${request.method}`);
    }
  }

  /**
   * Extract resource name from URI
   */
  private extractResourceName(uri: string): string | null {
    // Handle different URI schemes
    // For namespaced resources like "everything.Resource 1", extract the full name

    // First, try to match against known resource patterns
    const capabilities = this.capabilityAggregator.getCapabilities();

    // Find a resource that matches this URI
    for (const resource of capabilities.resources) {
      if (resource.uri === uri) {
        return resource.name;
      }
    }

    // Fallback: extract from URI path
    const match = uri.match(/^[^:]+:\/\/(.+)/);
    return match ? match[1] : null;
  }

  /**
   * Create error response
   */
  private createErrorResponse(id: any, code: number, message: string): any {
    return {
      jsonrpc: '2.0',
      id,
      error: {
        code,
        message
      }
    };
  }
}
