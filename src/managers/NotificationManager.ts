/**
 * Notification Manager - Handles notifications from backend servers
 */

import { EventEmitter } from 'events';
import { JSONRPCNotification } from '@modelcontextprotocol/sdk/types.js';
import { BackendManager } from './BackendManager.js';
import { logger } from '../utils/logger.js';

export class NotificationManager extends EventEmitter {
  private pendingNotifications = new Set<string>();
  private notificationDebounceMs = 100; // Debounce notifications
  private enabled = false; // Only send notifications when enabled

  constructor(private backendManager: BackendManager) {
    super();
    this.setupBackendListeners();
  }

  /**
   * Enable notification forwarding
   */
  enable(): void {
    this.enabled = true;
    logger.debug('Notification forwarding enabled');
  }

  /**
   * Disable notification forwarding
   */
  disable(): void {
    this.enabled = false;
    logger.debug('Notification forwarding disabled');
  }

  /**
   * Set up listeners for backend manager events
   */
  private setupBackendListeners(): void {
    this.backendManager.on('capabilities-updated', (serverId: string) => {
      this.handleCapabilitiesUpdated(serverId);
    });

    this.backendManager.on('server-connected', (serverId: string) => {
      logger.debug(`Server connected: ${serverId}`);
      // Could emit a notification about server availability
    });

    this.backendManager.on('server-disconnected', (serverId: string, error?: Error) => {
      logger.debug(`Server disconnected: ${serverId}`, error ? { error: error.message } : {});
      // Could emit a notification about server unavailability
    });
  }

  /**
   * Handle capabilities updated event
   */
  private handleCapabilitiesUpdated(serverId: string): void {
    logger.debug(`Capabilities updated for server: ${serverId}, enabled: ${this.enabled}`);

    // Don't send notifications during startup - only when explicitly enabled
    if (!this.enabled) {
      logger.debug('Notification forwarding disabled, skipping capability change notifications');
      return;
    }

    // Determine what type of capabilities changed
    const connection = this.backendManager.getConnection(serverId);
    if (!connection) return;

    // Debounce notifications to avoid spam
    this.debounceNotification('tools/list_changed', () => {
      logger.debug('Attempting to emit tools/list_changed notification');
      this.emitNotification({
        jsonrpc: '2.0',
        method: 'notifications/tools/list_changed'
      });
    });

    this.debounceNotification('resources/list_changed', () => {
      logger.debug('Attempting to emit resources/list_changed notification');
      this.emitNotification({
        jsonrpc: '2.0',
        method: 'notifications/resources/list_changed'
      });
    });

    this.debounceNotification('prompts/list_changed', () => {
      logger.debug('Attempting to emit prompts/list_changed notification');
      this.emitNotification({
        jsonrpc: '2.0',
        method: 'notifications/prompts/list_changed'
      });
    });
  }

  /**
   * Debounce notifications to prevent spam
   */
  private debounceNotification(key: string, callback: () => void): void {
    if (this.pendingNotifications.has(key)) {
      return; // Already pending
    }

    this.pendingNotifications.add(key);

    setTimeout(() => {
      this.pendingNotifications.delete(key);
      // Check if notifications are still enabled when the timeout fires
      if (this.enabled) {
        callback();
      } else {
        logger.debug(`Notification ${key} skipped - forwarding disabled`);
      }
    }, this.notificationDebounceMs);
  }

  /**
   * Emit a notification to the client
   */
  private emitNotification(notification: JSONRPCNotification): void {
    if (!this.enabled) {
      logger.debug(`Notification forwarding disabled, skipping: ${notification.method}`);
      return;
    }

    logger.debug(`Emitting notification: ${notification.method}`);
    this.emit('notification', notification);
  }

  /**
   * Handle a notification from a backend server
   */
  handleBackendNotification(serverId: string, notification: JSONRPCNotification): void {
    logger.debug(`Received notification from ${serverId}: ${notification.method}`);

    // Forward certain notifications directly
    switch (notification.method) {
      case 'notifications/tools/list_changed':
      case 'notifications/resources/list_changed':
      case 'notifications/prompts/list_changed':
        // Trigger capabilities update when backend capabilities change
        logger.debug(`Backend ${serverId} capabilities changed: ${notification.method}`);
        this.emit('backend-capabilities-changed', serverId, notification.method);
        break;

      case 'notifications/progress':
        // Forward progress notifications directly
        this.emitNotification(notification);
        break;

      default:
        // Forward unknown notifications
        logger.debug(`Forwarding unknown notification: ${notification.method}`);
        this.emitNotification(notification);
        break;
    }
  }

  /**
   * Send a notification to all backend servers
   */
  async broadcastToBackends(notification: JSONRPCNotification): Promise<void> {
    const connections = this.backendManager.getAllConnections();
    const connectedServers = connections.filter(conn => conn.connected);

    logger.debug(`Broadcasting notification to ${connectedServers.length} servers: ${notification.method}`);

    const promises = connectedServers.map(async (connection) => {
      try {
        await connection.client.sendNotification(notification);
      } catch (error) {
        logger.error(`Failed to send notification to ${connection.id}:`, error);
      }
    });

    await Promise.allSettled(promises);
  }

  /**
   * Set notification debounce time
   */
  setDebounceTime(ms: number): void {
    this.notificationDebounceMs = ms;
  }
}
